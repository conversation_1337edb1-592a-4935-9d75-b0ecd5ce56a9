package com.guanwei.tmis.practitioner;

import cn.hutool.core.date.DateUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.tmis.common.enums.EnumCertificateState;
import com.guanwei.tmis.common.enums.EnumPracCertType;
import com.guanwei.tmis.practitioner.common.dto.PracSupervisionDTO;
import com.guanwei.tmis.practitioner.common.entity.PracCertificate;
import com.guanwei.tmis.practitioner.common.entity.PracSupervision;
import com.guanwei.tmis.practitioner.common.entity.Practitioner;
import com.guanwei.tmis.practitioner.common.mapper.PracSupervisionMapper;
import com.guanwei.tmis.practitioner.common.mapper.PractitionerMapper;
import com.guanwei.tmis.practitioner.common.service.PracCertificateService;
import com.guanwei.tmis.practitioner.common.service.PracSupervisionService;
import com.guanwei.tmis.practitioner.common.service.PractitionerService;
import com.guanwei.token.model.UserInfo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.net.URL;
import java.util.ArrayList;
import java.util.Date;

/**
 * 重点监管人员名单测试
 *
 * <AUTHOR>
 * @date 2024/10/11
 */
@SpringBootTest
public class PracSupervisionTest {

    @Autowired
    private PracSupervisionService pracSupervisionService;

    @Autowired
    private PractitionerService practitionerService;

    @Autowired
    private PractitionerMapper practitionerMapper;

    @Autowired
    private PracCertificateService pracCertificateService;

    @Autowired
    private PracSupervisionMapper supervisionMapper;
    @Test
    void test() {

        String[] a = new String[]{
        "342122197206231812",
        "32020319901226187X",
        "340822198904151634",
        "522422199004256076",
        "510603199607240493",
        "320106198107173214",
        "341222197911168470",
        "320211196708090016",
        "320211197412130730",
        "320222197006305717",
        "320211198108054512",
        "320921196807295173",
        "412722198703254930",
        "320223197810055217",
        "340823197905035610",
        "320923198105134211",
        "321283197301154230",
        "320219197010116355",
        "320222197512030218",
        "320826198209090616",
        "320211198302071615",
        "342421197109063511",
        "232128198112210030",
        "321321197205145533",
        "320222198201100017",
        "320283198304166310",
        "34242319920714857X",
        "510725198310198019",
        "230811198701172617",
        "320324197609217036",
        "352230197910020012",
        "342502198609217418",
        "320822197703211815",
        "321083199008092892",
        "320204196707102317",
        "320825197607045617",
        "34222519790801207X",
        "410523198709066539",
        "341222199001153252",
        "320283198712066511",
        "320222198206216510",
        "32092519770123513X",
        "320723196803163458",
        "320922198208304213",
        "320921196807104613",
        "320211198111200015",
        "341222198705225010",
        "342423198401028278",
        "320211197502230030",
        "422202198311212511",
        "320382197606067814",
        "342421197008083572",
        "320204198501093012",
        "341221199206207596",
        "321322198605225234",
        "320211199007041610",
        "320211199206104135",
        "320721196803240254",
        "510781198210043037",
        "320586199402242110",
        "320922198304051738",
        "320222197710101515",
        "320219197911167774",
        "321023199007231030",
        "342225198707285754",
        "320222198005050817",
        "320830198608150019",
        "500226198510276875",
        "320202197211121532",
        "320204196912182010",
        "341222197306082951",
        "342122197210061916",
        "342623197310064819",
        "320222197902177573",
        "310104197307255637",
        "320222197212261196",
        "320423197911261416",
        "341021198706202972",
        "342421196505153215",
        "320202198709012039",
        "413026198309173911",
        "320219198111179077",
        "320203197002191215",
        "32072319790901341X",
        "372523198206053938",
        "321321198409063139",
        "320222197911196514",
        "320222197504165016",
        "320830198901162019",
        "320923199006264816",
        "320211199310300734",
        "320219197505307014",
        "321281199101012293",
        "34122219861227557X",
        "320222197601010825",
        "320281199206306510",
        "34122719870906907X",
        "320283198908313019",
        "320421196506282312",
        "320923198505103318",
        "511224198007186290",
        "320830197007256614",
        "420700196503145775",
        "342425199006214713",
        "320926197712305291",
        "522325199209044070",
        "32021919760903601X",
        "320219197409076076",
        "320724198802250918",
        "320219197811081332",
        "320222198009152490",
        "342423197502035675",
        "511323198105220375",
        "320211197509281615",
        "320219196602072275",
        "342222199110296018",
        "342222197608202155",
        "370982198202083451",
        "340621199201026615",
        "320281199407275773",
        "320921198310107252",
        "320924198403191739",
        "410224198011204856",
        "341182198701212619",
        "23020419851025041X",
        "320283198803151375",
        "320682199610141891",
        "341522198809053977",
        "320223197301176774",
        "412722197611116931",
        "410327199412065315",
        "320219197602095519",
        "320219198103267018",
        "321323198506225518",
        "320219198510104531",
        "320222196511232075",
        "320219197505276510",
        "32021919650815627X",
        "342221196708045539",
        "320219197309085776",
        "51072719810320081X",
        "320281198710201799",
        "142729198810121833",
        "340421198011152017",
        "320219197708156295",
        "320924199004121754",
        "342130197807105619",
        "513030198612175010",
        "320281199603156018",
        "320202196909251019",
        "320219196411151296",
        "320219197708185037",
        "320219197407276058",
        "340822196808315513",
        "320202197911280518",
        "510723199211060534",
        "320219197712097013",
        "320211197804280439",
        "341225198508138515",
        "320219197809132276",
        "320923198107130919",
        "320922197606093939",
        "342823196902280415",
        "320219197411035775",
        "372922198810151716",
        "34122419781119683X",
        "342601197608071853",
        "320921198610073453",
        "320219198401075276",
        "341204198702221214",
        "341225197703121255",
        "32021919790919903X",
        "520203198704063914",
        "500235198710226270",
        "430381198410291957",
        "320221197107117778",
        "341282199210081474",
        "320830198611261414",
        "32072419900705631X",
        "320219197406302015",
        "320381198401246737",
        "320223197712026471",
        "320211198204102238",
        "320283198608160030",
        "321281198412013694",
        "320202197801304013",
        "522425198602176118",
        "342422198007063597",
        "320219197901062514",
        "41272219780318201X",
        "320282197410140519",
        "320283198809050073",
        "412723198706038113",
        "320322198211115912",
        "32082119850806271X",
        "321085198106122459",
        "320582198408110813",
        "320981198511175717",
        "320219196411226294",
        "37040619890721661X",
        "340826198106240312",
        "320222196910124821",
        "320219197105056797",
        "320106197610012819",
        "510724197806172014",
        "340223196412087458",
        "321023196804266435",
        "320219197406227019",
        "51112219740502151X",
        "510725196804206614",
        "34242219951216131X",
        "321083197504013058",
        "513701198407283529",
        "342427198012034813",
        "340821197609265339",
        "320219198102221798",
        "320219197103271514",
        "320282199210184110",
        "320219197311264511",
        "34262219900227709X",
        "34032319860615377X",
        "320281199303175276",
        "32028219870502021X",
        "150429198904300019",
        "320219197211025775",
        "320219197504145770",
        "320911198112280916",
        "320219198108103274",
        "320202198512090017",
        "320826197909245637",
        "321281198709091554",
        "320382198606175019",
        "320219196610157770",
        "321024197808162212",
        "320219198001106774",
        "321323198903240033",
        "410622199103144014",
        "412723199211128197",
        "320219197902136298",
        "372928198708175437",
        "320223197610187215",
        "412822197612018116",
        "341282199511101416",
        "341126199102257210",
        "320683198405218417",
        "320830198107140410",
        "420502198409151133",
        "520221198403200471",
        "320924199610093458",
        "411328199112164618",
        "320282199011132297",
        "412826197609283117",
        "320219198110195016",
        "320211198011253419",
        "32102319760612481X",
        "320201198005203033",
        "320222197609172079",
        "152224197609226017",
        "522527198808291320",
        "32132319921214491X",
        "320921198809206815",
        "320281198710032518",
        "340121199001194019",
        "341222198010035034",
        "320219197509266272",
        "140428199302285655",
        "620502199309206354",
        "320281198705255514",
        "412702198208191818",
        "342601198304200278",
        "413026199101250935",
        "232325199602011436",
        "32021919831018301X",
        "320826198308163219",
        "320219196806273511",
        "320201196810312014",
        "512930197312273771",
        "320219197212103317",
        "320921197404113819",
        "320219197312083771",
        "320281199408301777",
        "320219197212151511",
        "320202198610174513",
        "320219196704046270",
        "321283197401152419",
        "320211197808270713",
        "342422198001042576"
        };

        UserInfo userInfo = new UserInfo();
        userInfo.setUserName("韩晶");
        userInfo.setUserId("d2e3f002b7794c92b6f3e495a74940cf");
        userInfo.setOrgName("无锡市交通运输局");
        userInfo.setDepName("执法监督二大队（市级政务服务分中心）");
        for (String s : a) {
            LambdaQueryWrapper<Practitioner> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Practitioner::getIdCertNum,s);
            wrapper.eq(Practitioner::getAreaCode,"320200");
            Practitioner one = practitionerService.getOne(wrapper, false);
            if(EmptyUtil.isNotEmpty(one)){
                LambdaQueryWrapper<PracCertificate> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.eq(PracCertificate::getPracId, one.getPracId());
                wrapper1.eq(PracCertificate::getCertType, EnumPracCertType.Normal.getValue());
                wrapper1.eq(PracCertificate::getCertState, EnumCertificateState.Valid.getValue());
                PracCertificate pracCertificate = pracCertificateService.getOne(wrapper1, false);
                String pracCertNum = null;
                String pracTypeCodes = null;
                String pracTypeNames = null;
                if(EmptyUtil.isNotEmpty(pracCertificate)){
                    pracCertNum = pracCertificate.getPracCertNum();
                    pracTypeCodes = pracCertificate.getPracTypeCodes();
                    pracTypeNames = pracCertificate.getPracTypeNames();
                } else {
                    wrapper1.clear();
                    wrapper1.eq(PracCertificate::getPracId, one.getPracId());
                    wrapper1.eq(PracCertificate::getCertType, EnumPracCertType.NetDriver.getValue());
                    wrapper1.eq(PracCertificate::getCertState, EnumCertificateState.Valid.getValue());
                    pracCertificate = pracCertificateService.getOne(wrapper1, false);
                    if(EmptyUtil.isNotEmpty(pracCertificate)){
                        pracCertNum = pracCertificate.getPracCertNum();
                        pracTypeCodes = pracCertificate.getPracTypeCodes();
                        pracTypeNames = pracCertificate.getPracTypeNames();
                    }
                }

                PracSupervision pracSupervision = new PracSupervision();
                pracSupervision.setPracName(one.getPracName());
                pracSupervision.setIdCertNum(one.getIdCertNum());
                pracSupervision.setPracCertNum(pracCertNum);
                pracSupervision.setStartDate(new Date());
                pracSupervision.setPracId(one.getPracId());
                pracSupervision.setReason("根据公安抄告，从业资格证待撤销");
                pracSupervision.setRecorder(userInfo.userName);
                pracSupervision.setUserId(userInfo.userId);
                pracSupervision.setRecordTime(new Date());
                pracSupervision.setOrgName(userInfo.orgName);
                pracSupervision.setDepName(userInfo.depName);
                pracSupervision.setTransType(0);
                pracSupervision.setPracTypeCodes(pracTypeCodes);
                pracSupervision.setPracTypeNames(pracTypeNames);
                pracSupervision.setAreaCode("320200");
                /*supervisionMapper.insert(pracSupervision);*/
            }
        }
    }
}
