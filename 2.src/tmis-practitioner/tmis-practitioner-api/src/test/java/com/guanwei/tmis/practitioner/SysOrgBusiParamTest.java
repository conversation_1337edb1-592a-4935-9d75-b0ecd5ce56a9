package com.guanwei.tmis.practitioner;

import com.guanwei.tmis.common.enums.EnumPracType;
import com.guanwei.tmis.common.service.CodeRuleService;
import com.guanwei.tmis.common.service.SysBusiParamService;
import com.guanwei.tmis.practitioner.common.service.PracApplyService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class SysOrgBusiParamTest {
    @Autowired
    private SysBusiParamService sysBusiParamService;

    @Autowired
    private PracApplyService pracApplyService;
    @Test
    @DisplayName("验证测试")
    void getSysOrgParam() {
        boolean isAlone =
                sysBusiParamService.getBusiParamBoolValue("7514bcd329d8436da72fe96d6e8e797a", SysBusiParamService.PRAC_MEGER_CERT);
        System.out.println("是否单独发证:"+isAlone);
        String number = "";
        if (isAlone && "09002".equals(EnumPracType.PracTypeCodeNetDriver.getValue())) {
            //从业人员档案号编码规则采用的是系统管理中的机构编码规则中的从业人员档案编码类别；
            number = pracApplyService.generateNumberByRecursive("7514bcd329d8436da72fe96d6e8e797a", CodeRuleService.PRAC_NO_AP_TAXI);
        } else {
            //从业人员档案号编码规则采用的是系统管理中的机构编码规则中的从业人员档案编码类别；
             number = pracApplyService.generateNumberByRecursive("7514bcd329d8436da72fe96d6e8e797a", CodeRuleService.PRAC_NO);
        }
        System.out.println("number:"+number);
    }
}
