package com.guanwei.tmis.practitioner;

import com.alibaba.fastjson2.JSON;
import com.guanwei.tmis.practitioner.common.dto.PracRegisterPageDTO;
import com.guanwei.tmis.practitioner.common.query.PracRegisterQuery;
import com.guanwei.tmis.practitioner.common.service.PracRegisterService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class SelectPracRegisterTest {
    @Autowired
    private PracRegisterService pracRegisterService;

    @Test
    @DisplayName("单元功能测试")
    void getRegisterList() {
        PracRegisterQuery pracRegisterQuery = new PracRegisterQuery();
        pracRegisterQuery.setIncludeSubOrgCode("JTT-ZFJ");
        pracRegisterQuery.setTransType(90);
        List<PracRegisterPageDTO> registerPageDTOS = pracRegisterService.getRegisterDTO(pracRegisterQuery);
        System.out.println("结果" + JSON.toJSONString(registerPageDTOS));
    }
}
