package com.guanwei.tmis.practitioner;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.IOException;

public class JacksonTest {
    public static void main(String[] args) throws IOException {
        ObjectMapper mapper = new ObjectMapper();

        ObjectNode childNode1 = mapper.createObjectNode();
        childNode1.put("name1", "val1");
        childNode1.put("name2", "val2");

        ObjectNode childNode2 = mapper.createObjectNode();
        childNode2.put("name3", "val3");
        childNode2.put("name4", "val4");

        ObjectNode childNode3 = mapper.createObjectNode();
        childNode3.put("name5", "val5");
        childNode3.put("name6", "val6");

        ArrayNode arrayNode = mapper.createArrayNode();
        arrayNode.add(childNode1);
        arrayNode.add(childNode2);
        arrayNode.add(childNode3);

        ObjectNode rootNode = mapper.createObjectNode();
        rootNode.set("obj1", childNode1);
        rootNode.set("obj2", childNode2);
        rootNode.set("obj3", childNode3);
        rootNode.set("obj4", arrayNode);

        String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
        System.out.println(jsonString);

        ObjectNode jsonNodes = mapper.readValue(jsonString, ObjectNode.class);
        System.out.println(jsonNodes.get("obj4"));
        System.out.println(jsonNodes);
    }
}
