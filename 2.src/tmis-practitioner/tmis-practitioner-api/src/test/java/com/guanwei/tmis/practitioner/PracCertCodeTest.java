package com.guanwei.tmis.practitioner;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.guanwei.tmis.common.enums.EnumApplyState;
import com.guanwei.tmis.common.enums.EnumCertificateState;
import com.guanwei.tmis.practitioner.common.entity.PracApply;
import com.guanwei.tmis.practitioner.common.entity.PracCertificate;
import com.guanwei.tmis.practitioner.common.service.PracApplyService;
import com.guanwei.tmis.practitioner.common.service.PracCertificateService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest
public class PracCertCodeTest {

    @Autowired
    private PracCertificateService pracCertificateService;

    @Test
    public void test() {
        //重新查询一遍
        LambdaQueryWrapper<PracCertificate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PracCertificate::getPracId, "7616555db7a84a72a4c7cb4ae5cad708");
        lambdaQueryWrapper.eq(PracCertificate::getCertState, EnumCertificateState.Valid.getValue());
        List<PracCertificate> list = pracCertificateService.list(lambdaQueryWrapper);

        String collect = list.stream().map(PracCertificate::getPracTypeCodes).distinct().collect(Collectors.joining(","));
        String[] split = collect.split(",");
        Arrays.sort(split);
        System.out.println(collect);
        System.out.println(split);
        String na = "";

        String collect1 = String.join(",", split);

        for (String s : split) {
            String.join(na, s, ",");
        }
    }
}
