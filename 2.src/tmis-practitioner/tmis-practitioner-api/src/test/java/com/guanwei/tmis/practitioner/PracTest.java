package com.guanwei.tmis.practitioner;

import com.guanwei.tmis.common.entity.SysOrganize;
import com.guanwei.tmis.common.enums.EnumIndustryClass;
import com.guanwei.tmis.common.enums.EnumPracApplyType;
import com.guanwei.tmis.common.enums.EnumPracCertType;
import com.guanwei.tmis.common.enums.EnumPracType;
import com.guanwei.tmis.common.service.CodeRuleService;
import com.guanwei.tmis.common.service.SysBusiParamService;
import com.guanwei.tmis.common.service.SysOrganizeService;
import com.guanwei.tmis.practitioner.common.entity.PracApply;
import com.guanwei.tmis.practitioner.common.entity.PracApplyBase;
import com.guanwei.tmis.practitioner.common.entity.PracApplyCert;
import com.guanwei.tmis.practitioner.common.service.PracApplyBaseService;
import com.guanwei.tmis.practitioner.common.service.PracApplyCertService;
import com.guanwei.tmis.practitioner.common.service.PracApplyService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.Objects;

@SpringBootTest
public class PracTest {

    @Autowired
    private PracApplyService pracApplyService;

    @Autowired
    private PracApplyBaseService pracApplyBaseService;

    @Autowired
    private PracApplyCertService pracApplyCertService;

    @Autowired
    private SysBusiParamService sysBusiParamService;

    @Autowired
    private SysOrganizeService sysOrganizeService;

    @Test
    public void test() {
        PracApply pracApply = pracApplyService.getById("2dde05ecb43d414ba70e67c0bdc7daba");
        PracApplyBase pracApplyBase = pracApplyBaseService.getById("2dde05ecb43d414ba70e67c0bdc7daba");

        PracApplyCert pracApplyCert = new PracApplyCert();
        pracApplyCert.setCertType(EnumPracCertType.NetDriver.getValue());
        SysOrganize issueOrganize = sysOrganizeService.getIssueOrganize(pracApply.getOrgId(), EnumIndustryClass.Practitioner.getValue());
        Assert.notNull(issueOrganize, "未计算得出发证机构信息！");

        //初领增类在受理和制证归档环节发证机构
        pracApplyCert.setPracCertFirstDate(new Date());
        pracApplyCert.setPracCertStartDate(new Date());
        pracApplyCert.setPracCertIssueDate(new Date());
        pracApplyCert.setIssueOrgId(issueOrganize.getOrgId());
        pracApplyCert.setIssueOrgName(issueOrganize.getOrgName());

        Date pracCertEndDate = pracApplyService.calcPracCertEndDate(pracApply, pracApplyBase, pracApplyCert.getPracCertStartDate());
        Assert.notNull(pracCertEndDate, "未能计算得到从业资格证有效期止时间！");
        pracApplyCert.setPracCertEndDate(pracCertEndDate);

        pracApplyCert.setCertNum(pracApply.getIdCertNum());
        pracApplyCert.setPracCertNum(pracApply.getIdCertNum());


        boolean isAlone = sysBusiParamService.getBusiParamBoolValue(issueOrganize.getOrgId(), SysBusiParamService.PRAC_MEGER_CERT);

        if (Objects.equals(EnumPracApplyType.First.getValue(), pracApply.getApplyType())) {
            String number;
            if (isAlone && pracApply.getPracTypeCodes().equals(EnumPracType.PracTypeCodeNetDriver.getValue())) {
                //从业人员档案号编码规则采用的是系统管理中的机构编码规则中的从业人员档案编码类别；档案归管辖机构管，所以档案号生成规则取管辖机构的
                number = pracApplyService.generateNumberByRecursive(pracApply.getOrgId(), CodeRuleService.PRAC_NO_AP_TAXI);
            } else {
                //从业人员档案号编码规则采用的是系统管理中的机构编码规则中的从业人员档案编码类别；档案归管辖机构管，所以档案号生成规则取管辖机构的
                number = pracApplyService.generateNumberByRecursive(pracApply.getOrgId(), CodeRuleService.PRAC_NO);
            }
            pracApplyCert.setArchiveNum(number);
        } else {
            //增类，申请的从业资格是网约，且不合并发证
            if (isAlone && pracApply.getPracTypeCodes().equals(EnumPracType.PracTypeCodeNetDriver.getValue())) {
                //从业人员档案号编码规则采用的是系统管理中的机构编码规则中的从业人员档案编码类别；
                String number = pracApplyService.generateNumberByRecursive(pracApply.getOrgId(), CodeRuleService.PRAC_NO_AP_TAXI);
                pracApplyCert.setArchiveNum(number);
            }
        }
        Assert.notNull(pracApplyCert.getCertType(), "【PR_PracApplyCert】申请表中从业资格证的证件类型【certType】不能为空！");
        pracApplyCert.setApplyId(pracApply.getApplyId());
        pracApplyCertService.saveOrUpdate(pracApplyCert);
    }
}
