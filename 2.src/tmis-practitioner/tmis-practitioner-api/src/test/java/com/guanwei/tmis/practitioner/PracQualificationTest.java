package com.guanwei.tmis.practitioner;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.guanwei.core.ApplicationContextHolder;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.tmis.common.entity.SPCatalogCoding;
import com.guanwei.tmis.common.enums.EnumCertificateState;
import com.guanwei.tmis.common.enums.EnumConstCatalogCoding;
import com.guanwei.tmis.common.enums.EnumPracType;
import com.guanwei.tmis.common.service.SPCatalogCodingService;
import com.guanwei.tmis.practitioner.common.entity.PracCertificate;
import com.guanwei.tmis.practitioner.common.entity.PracQualification;
import com.guanwei.tmis.practitioner.common.entity.Practitioner;
import com.guanwei.tmis.practitioner.common.service.PracCertificateService;
import com.guanwei.tmis.practitioner.common.service.PracQualificationService;
import com.guanwei.tmis.practitioner.common.service.PractitionerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.list.TreeList;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@AutoConfigureMockMvc
@SpringBootTest
class PracQualificationTest {


    @Autowired
    private PracQualificationService pracQualificationService;

    @Autowired
    private PractitionerService practitionerService;

    @Autowired
    private PracCertificateService pracCertificateService;

    @Test
    @DisplayName("单元功能测试")
    void contextLoads() {
        LambdaQueryWrapper<PracQualification> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.isNull(PracQualification::getPracTypeName);
        lambdaQueryWrapper.isNotNull(PracQualification::getPracTypeCode);
        List<PracQualification> list = pracQualificationService.list(lambdaQueryWrapper);
        for (PracQualification pracQualification : list) {
            Optional<EnumPracType> pracTypeOptional = EnumPracType.getByValue(pracQualification.getPracTypeCode());
            if (pracTypeOptional.isPresent()) {
                pracQualification.setPracTypeName(pracTypeOptional.get().getDescription());
                pracQualificationService.update(pracQualification);
            }
        }
        Assertions.assertTrue(true);
    }

    @Test
    @DisplayName("去除重复资格")
    void resetQualification() {
        PractitionerService practitionerService = ApplicationContextHolder.getBean(PractitionerService.class);
        PracCertificateService pracCertificateService = ApplicationContextHolder.getBean(PracCertificateService.class);
        SPCatalogCodingService spCatalogCodingService = ApplicationContextHolder.getBean(SPCatalogCodingService.class);
        PracQualificationService pracQualificationService = ApplicationContextHolder.getBean(PracQualificationService.class);

        ArrayList<String> pracIds = new ArrayList<>();

        pracIds.add("test1");
        pracIds.add("test2");
        pracIds.add("test3");
        pracIds.add("test4");
        LambdaQueryWrapper<Practitioner> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Practitioner::getPracId, pracIds);
        List<Practitioner> practitioners = practitionerService.list(queryWrapper);

        practitioners.forEach(item -> {
            String pracId = item.getPracId();

            // 重新查询一遍
            LambdaQueryWrapper<PracCertificate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(PracCertificate::getPracId, pracId);
            lambdaQueryWrapper.eq(PracCertificate::getCertState, EnumCertificateState.Valid.getValue());
            List<PracCertificate> list = pracCertificateService.list(lambdaQueryWrapper);

            // 先删除所有资格
            LambdaQueryWrapper<PracQualification> qualificationWrapper = new LambdaQueryWrapper<>();
            qualificationWrapper.eq(PracQualification::getPracId, pracId);
            pracQualificationService.remove(qualificationWrapper);

            // 证件表去除重复资格并且重新排序
            list.forEach(a -> {
                List<String> sortedCodes = Arrays.stream(a.getPracTypeCodes().split(","))
                        .distinct()
                        .sorted()
                        .toList();
                List<String> totalNamesList = new ArrayList<>();
                sortedCodes.forEach(i -> {
                    LambdaQueryWrapper<SPCatalogCoding> codingLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    codingLambdaQueryWrapper.eq(SPCatalogCoding::getCodingClass, EnumConstCatalogCoding.PRACTYPE_CODE.getValue());
                    codingLambdaQueryWrapper.eq(SPCatalogCoding::getCode, i);
                    SPCatalogCoding spCatalogCoding = spCatalogCodingService.getOne(codingLambdaQueryWrapper, false);
                    if (EmptyUtil.isNotEmpty(spCatalogCoding)) {
                        totalNamesList.add(spCatalogCoding.getCodeName());
                    }
                });
                if (EmptyUtil.isNotEmpty(totalNamesList)) {
                    String totalSortedCodes = String.join(",", sortedCodes);
                    String totalNames = String.join(",", totalNamesList);

                    LambdaUpdateWrapper<PracCertificate> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.eq(PracCertificate::getPracCertId, a.getPracCertId());
                    wrapper.set(PracCertificate::getPracTypeCodes, totalSortedCodes);
                    wrapper.set(PracCertificate::getPracTypeNames, totalNames);
                    pracCertificateService.update(wrapper);

                    // 资格表去除重复资格
                    List<PracQualification> pracQualifications = new ArrayList<>();
                    for (String pracTypeCode : sortedCodes) {
                        Optional<EnumPracType> pracTypeOptional = EnumPracType.getByValue(pracTypeCode);
                        if (pracTypeOptional.isPresent()) {
                            PracQualification pracQualification = new PracQualification();
                            pracQualification.setPracTypeCode(pracTypeCode);
                            pracQualification.setPracTypeName(pracTypeOptional.get().getDescription());
                            pracQualification.setPracId(pracId);
                            pracQualification.setPracCertId(a.getPracCertId());
                            pracQualification.setFirstDate(a.getPracCertFirstDate());
                            pracQualification.setIssueDate(a.getPracCertIssueDate());
                            pracQualification.setCertNum(a.getCertNum());
                            pracQualifications.add(pracQualification);
                        }
                    }
                    // 再重新新增
                    pracQualificationService.saveBatch(pracQualifications);
                }
            });
            // 证件表去除重复资格并且重新排序
            if (EmptyUtil.isNotEmpty(list)) {
                String originPracTypeCodes = list.stream()
                        .map(PracCertificate::getPracTypeCodes)
                        .collect(Collectors.joining(","));
                String pracTypeCodes = Arrays.stream(originPracTypeCodes.split(","))
                        .distinct()
                        .collect(Collectors.joining(","));

                if (EmptyUtil.isNotEmpty(pracTypeCodes)) {
                    String[] splitPracCodes = pracTypeCodes.split(",");
                    //为从业资格重新排序
                    Arrays.sort(splitPracCodes);

                    List<String> totalPracTypeNamesList = new TreeList<>();
                    for (String s : splitPracCodes) {
                        LambdaQueryWrapper<SPCatalogCoding> codingLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        codingLambdaQueryWrapper.eq(SPCatalogCoding::getCodingClass, EnumConstCatalogCoding.PRACTYPE_CODE.getValue());
                        codingLambdaQueryWrapper.eq(SPCatalogCoding::getCode, s);
                        SPCatalogCoding spCatalogCoding = spCatalogCodingService.getOne(codingLambdaQueryWrapper, false);
                        if (EmptyUtil.isNotEmpty(spCatalogCoding)) {
                            totalPracTypeNamesList.add(spCatalogCoding.getCodeName());
                        }
                    }
                    if (EmptyUtil.isNotEmpty(totalPracTypeNamesList)) {
                        String totalPracTypeCodes = String.join(",", splitPracCodes);
                        String totalPracTypeNames = String.join(",", totalPracTypeNamesList);

                        LambdaUpdateWrapper<Practitioner> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                        lambdaUpdateWrapper.eq(Practitioner::getPracId, pracId);
                        lambdaUpdateWrapper.set(Practitioner::getPracTypeCodes, totalPracTypeCodes);
                        lambdaUpdateWrapper.set(Practitioner::getPracTypeNames, totalPracTypeNames);
                        practitionerService.update(lambdaUpdateWrapper);
                    }
                }
            }
        });
    }

}
