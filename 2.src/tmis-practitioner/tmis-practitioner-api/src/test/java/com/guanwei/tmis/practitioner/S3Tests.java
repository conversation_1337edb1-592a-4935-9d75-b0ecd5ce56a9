package com.guanwei.tmis.practitioner;

import cn.hutool.core.date.DateUtil;
import com.amazonaws.services.s3.AmazonS3;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.net.URL;
import java.util.Date;

@SpringBootTest
public class S3Tests {

    @Autowired
    private AmazonS3 amazonS3;

    @Test
    void test() {
        boolean test = amazonS3.doesBucketExistV2("test");
        System.out.println(test);
    }

    @Test
    void getPreSignedObjectUrl() {
        URL url = amazonS3.generatePresignedUrl("tmis", "2024/08/30/8288a4f34d044c0eb77e092d9977ddeb.jpeg", DateUtil.offsetDay(new Date(), 3));
        System.out.println(url);
    }
}
