package com.guanwei.tmis.practitioner;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.tmis.common.dto.SPBusinessItemCodeDTO;
import com.guanwei.tmis.common.enums.*;
import com.guanwei.tmis.common.mapper.SPBusinessItemMapper;
import com.guanwei.tmis.common.query.SPBusinessItemCodeQuery;
import com.guanwei.tmis.practitioner.common.entity.PracApply;
import com.guanwei.tmis.practitioner.common.entity.PracApplyCert;
import com.guanwei.tmis.practitioner.common.entity.PracApplyRegister;
import com.guanwei.tmis.practitioner.common.mapper.PracApplyCertMapper;
import com.guanwei.tmis.practitioner.common.mapper.PracApplyMapper;
import com.guanwei.tmis.practitioner.common.mapper.PracApplyRegisterMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@SpringBootTest
public class SPBusinessItemTest {

    @Autowired
    private PracApplyMapper pracApplyMapper;

    @Autowired
    private PracApplyCertMapper pracApplyCertMapper;

    @Autowired
    private PracApplyRegisterMapper pracApplyRegisterMapper;

    @Autowired
    private SPBusinessItemMapper spBusinessItemMapper;

    @Test
    void test() {
        LambdaQueryWrapper<PracApply> pracApplyLambdaQueryWrapper = new LambdaQueryWrapper<>();
        pracApplyLambdaQueryWrapper.ge(PracApply::getApplyDate, DateUtil.parse("2024-11-11 00:00:00"));

        pracApplyMapper.selectList(pracApplyLambdaQueryWrapper, resultContext -> {
            PracApply pracApply = resultContext.getResultObject();
            //2、调整更新事项名称信息
            if (EmptyUtil.isEmpty(pracApply.getBusinessCode()) && EmptyUtil.isEmpty(pracApply.getBusinessName())) {
                SPBusinessItemCodeQuery spBusinessItemCodeQuery = new SPBusinessItemCodeQuery();
                Integer applyType = pracApply.getApplyType();
                Optional<EnumPracApplyType> enumPracApplyType = EnumPracApplyType.getByPracApplyType(applyType);
                PracApplyCert pracApplyCert;
                PracApplyRegister pracApplyRegister;
                if (enumPracApplyType.isPresent()) {
                    switch (enumPracApplyType.get()) {
                        // ## 1.初领 增类 pracTypeCodes 为单个code，对应了业务事项中的行业代码
                        case First:
                        case Add:
                            spBusinessItemCodeQuery.setIndustryCode(pracApply.getPracTypeCodes());
                            break;
                        // ## 2.根据资格证证件类型判断行业代码
                        case Replenish:
                        case Replace:
                        case TransferIn:
                        case TransferOut:
                        case Cancel:
                        case Revoke:
                        case Revocation:
                        case Change:
                        case Reduce:
                        case GeneralChange:
                        case InternalTransfer:
                        case RePrintCert:
                        case AddMaterial:
                        case TransferInAdd:
                        case ArchiveNumChange:
                        case ReplaceICCard:
                        case ChangeOrg:
                        case Recovery:
                        case Replace180:
                        case Merge:
                        case CreditAccess:
                        case ReLearn:
                        case LearnCleanRecord:
                        case WithHold:
                        case CancelWithHold:
                            pracApplyCert = pracApplyCertMapper.selectById(pracApply.getApplyId());
                            if (EmptyUtil.isNotEmpty(pracApplyCert)) {
                                if (Objects.equals(EnumPracCertType.Normal.getValue(), pracApplyCert.getCertType())) {
                                    spBusinessItemCodeQuery.setIndustryCode("0");
                                    break;
                                } else if (Objects.equals(EnumPracCertType.NetDriver.getValue(), pracApplyCert.getCertType())) {
                                    spBusinessItemCodeQuery.setIndustryCode(EnumPracType.PracTypeCodeNetDriver.getValue());
                                    break;
                                }
                            }
                            spBusinessItemCodeQuery.setIndustryCode("0");
                            break;
                        // ## 3.巡游注册、网约注册、备案 | 巡游注销注册、网约注销注册、撤销备案 | 巡游变更注册（无对应事项）、网约变更注册（无对应事项）、变更备案
                        case Register:
                        case CancelRegister:
                        case ChangeRegister:
                            pracApplyRegister = pracApplyRegisterMapper.selectById(pracApply.getApplyId());
                            if (EmptyUtil.isNotEmpty(pracApplyRegister)) {
                                if (Objects.equals(EnumOwnerType.Taxi.getValue(), pracApplyRegister.getTransType())) {
                                    // 巡游
                                    spBusinessItemCodeQuery.setIndustryCode(EnumPracType.TaxiTransport.getValue());
                                    break;
                                } else if (Objects.equals(EnumOwnerType.NetTaxi.getValue(), pracApplyRegister.getTransType())) {
                                    // 网约
                                    spBusinessItemCodeQuery.setIndustryCode(EnumPracType.PracTypeCodeNetDriver.getValue());
                                    break;
                                } else {
                                    // 备案
                                    if (pracApplyRegister.getSource().equals(EnumPracRegisterSource.NonPracCert.getValue())) {
                                        // 1) 非持证人员
                                        spBusinessItemCodeQuery.setIndustryCode(pracApplyRegister.getTransType() + "-" + pracApplyRegister.getSource());
                                    } else {
                                        // 2) 本籍从业人员 / 外籍从业人员 (持证人员)
                                        spBusinessItemCodeQuery.setIndustryCode(pracApplyRegister.getTransType().toString());
                                    }
                                    break;
                                }
                            }
                            spBusinessItemCodeQuery.setIndustryCode("0"); // 应该查不到对应业务事项
                            break;
                        // ## 4.巡游延续注册、网约延续注册
                        case ReRegister:
                            pracApplyRegister = pracApplyRegisterMapper.selectById(pracApply.getApplyId());
                            if (EmptyUtil.isNotEmpty(pracApplyRegister)) {
                                if (Objects.equals(EnumOwnerType.Taxi.getValue(), pracApplyRegister.getTransType())) {
                                    // 巡游
                                    spBusinessItemCodeQuery.setIndustryCode(EnumPracType.TaxiTransport.getValue());
                                    break;
                                } else if (Objects.equals(EnumOwnerType.NetTaxi.getValue(), pracApplyRegister.getTransType())) {
                                    // 网约
                                    spBusinessItemCodeQuery.setIndustryCode(EnumPracType.PracTypeCodeNetDriver.getValue());
                                    break;
                                }
                            }
                            spBusinessItemCodeQuery.setIndustryCode("0"); // 应该查不到对应业务事项
                            break;
                        default:
                            break;
                    }
                    spBusinessItemCodeQuery.setApplyType(pracApply.getApplyType());
                    spBusinessItemCodeQuery.setIndustryClass(EnumIndustryClass.Practitioner.getValue());
                    List<SPBusinessItemCodeDTO> list = spBusinessItemMapper.getSPBusinessItemCodeDTO(spBusinessItemCodeQuery);

                    if (EmptyUtil.isNotEmpty(list)) {
                        SPBusinessItemCodeDTO spBusinessItemCodeDTO1 = list.get(0);
                        LambdaUpdateWrapper<PracApply> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                        lambdaUpdateWrapper.eq(PracApply::getApplyId, pracApply.getApplyId());
                        lambdaUpdateWrapper.set(PracApply::getBusinessName, spBusinessItemCodeDTO1.getBusinessName());
                        lambdaUpdateWrapper.set(PracApply::getBusinessCode, spBusinessItemCodeDTO1.getBusinessCode());
                        pracApplyMapper.update(lambdaUpdateWrapper);
                    }
                }
            }
        });
    }
}
