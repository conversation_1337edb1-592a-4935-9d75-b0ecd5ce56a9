package com.guanwei.tmis.practitioner.service.impl;

import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import com.google.common.collect.Lists;
import com.guanwei.tmis.common.enums.*;
import org.springframework.util.Assert;
import com.guanwei.token.model.UserInfo;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.tmis.common.service.*;
import org.springframework.stereotype.Service;
import com.guanwei.workflow.model.WorkflowVar;
import cn.hutool.core.collection.CollectionUtil;
import com.guanwei.tmis.common.entity.SysOrganize;
import com.guanwei.tmis.practitioner.common.dto.*;
import com.guanwei.tmis.common.utils.WorkflowUtils;
import com.guanwei.workflow.service.WorkflowService;
import com.guanwei.tmis.practitioner.common.entity.*;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.guanwei.tmis.practitioner.common.service.*;
import org.springframework.context.ApplicationContext;
import com.guanwei.workflow.dto.WorkflowProcessDataDTO;
import com.guanwei.tmis.practitioner.common.dto.mapstruct.*;
import com.guanwei.tmis.common.dto.SPBusinessApplyRecipientDTO;
import com.guanwei.tmis.common.entity.SPBusinessApplyRecipient;
import org.springframework.transaction.annotation.Transactional;
import com.guanwei.tmis.practitioner.common.event.PracApplyEvent;
import com.guanwei.tmis.practitioner.service.PracApplyTransInService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.guanwei.tmis.common.mapstruct.SPBusinessApplyRecipientMapStruct;

import java.util.*;
import java.text.MessageFormat;

/**
 * 从业人员转入申请实现类
 *
 * <AUTHOR>
 * @date 2022/09/16 11:39:37
 */
@Slf4j
@SuppressWarnings("unused")
@Service
@RequiredArgsConstructor
public class PracApplyTransInServiceImpl implements PracApplyTransInService {

    private final WorkflowService workflowService;

    private final PracApplyMapStruct pracApplyMapStruct;

    private final PracApplyBaseMapStruct pracApplyBaseMapStruct;

    private final PracApplyTransferInMapStruct pracApplyTransferInMapStruct;

    private final PracApplyCertMapStruct pracApplyCertMapStruct;

    private final PracApplyMaterialMapStruct pracApplyMaterialMapStruct;

    private final PracApplyExamMapStruct pracApplyExamMapStruct;

    private final PracApplyExamResultMapStruct pracApplyExamResultMapStruct;

    private final SPBusinessApplyRecipientMapStruct spBusinessApplyRecipientMapStruct;

    private final PracAttachmentMapStruct pracAttachmentMapStruct;

    private final PracApplyService pracApplyService;

    private final PracApplyBaseService pracApplyBaseService;

    private final PracApplyTransferInService pracApplyTransferInService;

    private final PracApplyCertService pracApplyCertService;

    private final PracApplyExamService pracApplyExamService;

    private final PracApplyExamResultService pracApplyExamResultService;

    private final PracApplyMaterialService pracApplyMaterialService;

    private final PracQualificationService pracQualificationService;

    private final PractitionerService practitionerService;

    private final PracAttachmentService pracAttachmentService;

    private final SpArchiveNumRuleService spArchiveNumRuleService;

    private final PracCertificateService pracCertificateService;

    private final SysOrganizeService sysOrganizeService;

    private final SPBusinessApplyRecipientService spBusinessApplyRecipientService;

    private final PracApplyCommonInfoDTOMapStruct pracApplyBaseInfoOutMapStruct;

    private final ComBsVerifyService comBsVerifyService;

    private final SysBusiParamService sysBusiParamService;

    private final ApplicationContext applicationContext;

    private final CodeRuleService codeRuleService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submit(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        log.info("{},提交的信息:PracApplyCommonInfoDTO={},UserInfo={}", businessName(), pracApplyCommonInfoDTO, userInfo);
        //1.归档从业资格申请表
        //先拼接数据
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getSource())) {
            pracApplyCommonInfoDTO.setSource(EnumApplySource.WindowAccept.getValue());
        }
        pracApplyCommonInfoDTO.setState(EnumApplyState.Accepted.getValue());
        pracApplyCommonInfoDTO.setCheckState(EnumWebsiteTwoCheckState.None.getValue());
        pracApplyCommonInfoDTO.setReasonType(EnumPracReasonType.None.getValue());
        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();


        pracApplyCommonInfoDTO.setOrgId(userInfo.getOrgId());
        pracApplyCommonInfoDTO.setOrgName(userInfo.getOrgName());
        pracApplyCommonInfoDTO.setAreaCode(userInfo.getAreaCode());
        pracApplyCommonInfoDTO.setCreator(userInfo.getUserName());
        pracApplyCommonInfoDTO.setModifier(userInfo.getUserName());
        pracApplyCommonInfoDTO.setCreateTime(new Date());
        pracApplyCommonInfoDTO.setModifyTime(new Date());

        pracApplyCommonInfoDTO.setCheckState(EnumWebsiteTwoCheckState.None.getValue());
        pracApplyCommonInfoDTO.setReasonType(EnumPracReasonType.None.getValue());
        pracApplyCommonInfoDTO.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        pracApplyCommonInfoDTO.setAcceptDate(new Date());
        pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        pracApplyCommonInfoDTO.setState(EnumApplyState.Accepted.getValue());
        pracApplyCommonInfoDTO.setDecisionResult(EnumDecisionResult.None.getValue());


        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        PracApply pracApply = pracApplyMapStruct.toSource(pracApplyDTO);
        pracApplyService.saveOrUpdate(pracApply);

        //归档转入申请表
        PracApplyTransferInDTO pracApplyTransferInDTO = pracApplyCommonInfoDTO.getPracApplyTransferIn();
        PracApplyTransferIn pracApplyTransferIn = pracApplyTransferInMapStruct.toSource(pracApplyTransferInDTO);
        if (EmptyUtil.isEmpty(pracApplyTransferIn.getCreditLevel())) {
            pracApplyTransferIn.setCreditLevel(0);
        }
        if (EmptyUtil.isEmpty(pracApplyTransferIn.getEducState())) {
            pracApplyTransferIn.setEducState(EnumEducState.OnGOing.getValue());
        }
        if (EmptyUtil.isEmpty(pracApplyTransferIn.getEducClassHour())) {
            pracApplyTransferIn.setEducClassHour(0);
        }
        pracApplyTransferIn.setApplyId(pracApply.getApplyId());
        pracApplyTransferInService.saveOrUpdate(pracApplyTransferIn);

        //2.归档从业资格申请基础表

        PracApplyBase pracApplyBase = pracApplyBaseMapStruct.toSource(pracApplyBaseDTO);
        pracApplyBase.setApplyId(pracApply.getApplyId());
        pracApplyBase.setCreateTime(new Date());
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.saveOrUpdate(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.归档从业资格申请基础表
        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();
        String industryCode = "0";
        //查询网约车从业人员是否合并发证参数 取发证机构的机构参数 2023-05-24 已经与项目经历讨论确认
        SysOrganize issueOrganize = sysOrganizeService.getIssueOrganize(pracApply.getOrgId(),
                EnumIndustryClass.Practitioner.getValue());
        Assert.notNull(issueOrganize, "未计算得出发证机构信息！");
        boolean isAlone =
                sysBusiParamService.getBusiParamBoolValue(issueOrganize.getOrgId(), SysBusiParamService.PRAC_MEGER_CERT);
        if (isAlone && pracApply.getPracTypeCodes().equals(EnumPracType.PracTypeCodeNetDriver.getValue())) {
            //单独发证
            pracApplyCertDTO.setCertType(EnumPracCertType.NetDriver.getValue());
            industryCode = EnumPracType.PracTypeCodeNetDriver.getValue();
        } else {
            //转入时从业资格可选择多个，这里有可能同时选了网约资格和其他资格，需要结合是否合并发证作判断
            if (isAlone && pracApply.getPracTypeCodes().contains(EnumPracType.PracTypeCodeNetDriver.getValue())) {
                Assert.isTrue(true, "网约车从业资格为分开发证，请到网约资格管理做转入！");
            }
            pracApplyCertDTO.setCertType(EnumPracCertType.Normal.getValue());
        }
        //默认发证日期是受理当天，制证时再更新，有效期起止，初领日期按照用户提交，
        // 考试系统同步过来的数据这三个字段可能为空，受理时先赋值当天，归档时重新生成

        // 2025-07-14 10:15:53 转入的时候要加入截止日期的计算，不能用户填什么就赋值什么
        if (EmptyUtil.isNotEmpty(pracApplyCertDTO.getPracCertStartDate())) {
            Date pracCertEndDate = pracApplyService.calcPracCertEndDate(pracApply, pracApplyBase, pracApplyCertDTO.getPracCertStartDate());
            Date minDate = EmptyUtil.isEmpty(pracApplyCertDTO.getPracCertEndDate()) || pracCertEndDate.before(pracApplyCertDTO.getPracCertEndDate()) ? pracCertEndDate : pracApplyCertDTO.getPracCertEndDate();
            pracApplyCertDTO.setPracCertEndDate(minDate);
        }
        if (pracApplyCommonInfoDTO.getSource().equals(EnumApplySource.Other.getValue())) {
            pracApplyCertDTO.setPracCertStartDate(new Date());
            Date pracCertEndDate = pracApplyService.calcPracCertEndDate(pracApply, pracApplyBase, pracApplyCertDTO.getPracCertStartDate());
            Assert.notNull(pracCertEndDate, "未能计算得到新的有效期止时间！");
            pracApplyCertDTO.setPracCertEndDate(pracCertEndDate);
            pracApplyCertDTO.setPracCertFirstDate(new Date());
        }
        pracApplyCertDTO.setPracCertIssueDate(new Date());
        pracApplyCertDTO.setIssueOrgId(issueOrganize.getOrgId());
        pracApplyCertDTO.setIssueOrgName(issueOrganize.getOrgName());
        pracApplyCertDTO.setPracTypeCodes(pracApply.getPracTypeCodes());
        pracApplyCertDTO.setPracTypeNames(pracApply.getPracTypeNames());
        pracApplyCertDTO.setCertNum(pracApply.getIdCertNum());
        pracApplyCertDTO.setPracCertNum(pracApply.getIdCertNum());
        PracApplyCert pracApplyCert = pracApplyCertMapStruct.toSource(pracApplyCertDTO);

        pracApplyCert.setApplyId(pracApply.getApplyId());
        pracApplyCertService.saveOrUpdate(pracApplyCert);
        pracApplyCommonInfoDTO.setPracApplyCert(pracApplyCertDTO);
        //业务验证
        comBsVerifyService.bsCommonVerify(pracApplyCommonInfoDTO,
                EnumIndustryClass.Practitioner.getValue(), industryCode,
                pracApplyCommonInfoDTO.getApplyType(), pracApplyCommonInfoDTO.getOrgId());

        //4.材料信息，先把之前提交的材料删除，以新提交的材料清单重新入库
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        pracApplyMaterialService.savePracApplyMaterial(pracApplyMaterials, pracApply.getApplyId());

        //5.归档从业资格申请考试成绩表
        List<PracApplyExamResultDTO> pracApplyExamResults = pracApplyCommonInfoDTO.getPracApplyExamResults();
        if (EmptyUtil.isNotEmpty(pracApplyExamResults)) {
            List<PracApplyExamResult> list = new ArrayList<>();
            for (PracApplyExamResultDTO pracApplyExamResultDTO : pracApplyExamResults) {
                PracApplyExamResult pracApplyExamResult = pracApplyExamResultMapStruct.toSource(pracApplyExamResultDTO);
                pracApplyExamResult.setApplyId(pracApply.getApplyId());
                pracApplyExamResult.setExamType(1);
                pracApplyExamResult.setExamResult(1);
                pracApplyExamResult.setModifyTime(new Date());
                list.add(pracApplyExamResult);
            }
            pracApplyExamResultService.saveOrUpdateBatch(list);
        }

        // 5.归档申请人邮寄信息
        if (Objects.equals(pracApplyDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.saveOrUpdate(spBusinessApplyRecipient);
        }

        // 案号生成
        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //7.工作流
        StringBuilder bizObjectName = new StringBuilder(pracApplyDTO.getPracName());
        if (EmptyUtil.isNotEmpty(pracApply.getIdCertNum())) {
            bizObjectName.append(MessageFormat.format("（身份证：{0}）", pracApplyDTO.getIdCertNum()));
        }
        if (EmptyUtil.isNotEmpty(pracApplyCert.getArchiveNum())) {
            bizObjectName.append(MessageFormat.format("（档案号：{0}）", pracApplyCert.getArchiveNum()));
        }
        workflowService.startWorkflow(WorkflowUtils.convertToWorkflowUser(userInfo), pracApply.getApplyId(), pracApply.getBusinessNo(), bizObjectName, pracApplyCommonInfoDTO.getProcessData());

        //9.发布事件
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Created));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void accept(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        log.info("{},提交的信息:PracApplyCommonInfoDTO={},UserInfo={}", businessName(), pracApplyCommonInfoDTO, userInfo);
        //企业端登记时没有这些信息
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAccepter())) {
            pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAccepterId())) {
            pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptOrgId())) {
            pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptOrgName())) {
            pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptDate())) {
            pracApplyCommonInfoDTO.setAcceptDate(new Date());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getOperator())) {
            pracApplyCommonInfoDTO.setOperator(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getTelephone())) {
            pracApplyCommonInfoDTO.setTelephone(userInfo.getMobile());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getMobile())) {
            pracApplyCommonInfoDTO.setMobile(userInfo.getMobile());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getDepId())) {
            pracApplyCommonInfoDTO.setDepId(userInfo.getDepId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getDepName())) {
            pracApplyCommonInfoDTO.setDepName(userInfo.getDepName());
        }
        pracApplyCommonInfoDTO.setCheckState(EnumWebsiteTwoCheckState.None.getValue());
        pracApplyCommonInfoDTO.setReasonType(EnumPracReasonType.None.getValue());
        pracApplyCommonInfoDTO.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        pracApplyCommonInfoDTO.setAcceptDate(new Date());
        pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        pracApplyCommonInfoDTO.setState(EnumApplyState.Accepted.getValue());
        pracApplyCommonInfoDTO.setDecisionResult(EnumDecisionResult.None.getValue());
        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        PracApplyTransferInDTO pracApplyTransferInDTO = pracApplyCommonInfoDTO.getPracApplyTransferIn();
        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();

        //1.更新申请表
        String applyId = pracApplyDTO.getApplyId();
        PracApply pracApply = pracApplyService.getById(applyId);
        Assert.notNull(pracApply, "Id为【" + applyId + "】的从业人员申请信息已不存在！");

        pracApplyMapStruct.updateToSource(pracApplyDTO, pracApply);
        pracApply.setModifier(userInfo.getUserName());
        pracApply.setModifyTime(new Date());
        pracApplyService.updateById(pracApply);

        //归档转入申请表
        PracApplyTransferIn pracApplyTransferIn = pracApplyTransferInService.getById(applyId);
        Assert.notNull(pracApplyTransferIn, "Id为【" + applyId + "】的从业人员转入申请信息不存在！");
        pracApplyTransferInMapStruct.updateToSource(pracApplyTransferInDTO, pracApplyTransferIn);
        pracApplyTransferInService.updateById(pracApplyTransferIn);

        //2.申请基础信息
        PracApplyBase pracApplyBase = pracApplyBaseService.getById(applyId);
        Assert.notNull(pracApplyBase, "Id为【" + applyId + "】的从业人员申请基础信息不存在！");
        pracApplyBaseMapStruct.updateToSource(pracApplyBaseDTO, pracApplyBase);
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.updateById(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.证件信息
        PracApplyCert pracApplyCert = pracApplyCertService.getById(applyId);
        Assert.notNull(pracApplyCert, "Id为【" + applyId + "】的从业人员申请证件信息不存在！");
        String industryCode = "0";
        //查询网约车从业人员是否合并发证参数 取发证机构的机构参数 2023-05-24 已经与项目经历讨论确认
        SysOrganize issueOrganize = sysOrganizeService.getIssueOrganize(pracApply.getOrgId(),
                EnumIndustryClass.Practitioner.getValue());
        Assert.notNull(issueOrganize, "未计算得出发证机构信息！");
        boolean isAlone =
                sysBusiParamService.getBusiParamBoolValue(issueOrganize.getOrgId(), SysBusiParamService.PRAC_MEGER_CERT);
        if (isAlone && pracApply.getPracTypeCodes().equals(EnumPracType.PracTypeCodeNetDriver.getValue())) {
            //从业人员档案号编码规则采用的是系统管理中的机构编码规则中的从业人员档案编码类别；档案归管辖机构管，所以档案号生成规则取管辖机构的
            String number = pracApplyService.generateNumberByRecursive(pracApply.getOrgId(), CodeRuleService.PRAC_NO_AP_TAXI);
            log.info("管辖机构【{}】,生成的从业人员档案号：【{}】", pracApply.getOrgName(), number);
            pracApplyCertDTO.setArchiveNum(number);
            pracApplyCertDTO.setCertType(EnumPracCertType.NetDriver.getValue());
            industryCode = EnumPracType.PracTypeCodeNetDriver.getValue();
        } else {
            //转入时从业资格可选择多个，这里有可能同时选了网约资格和其他资格，需要结合是否合并发证作判断
            if (isAlone && pracApply.getPracTypeCodes().contains(EnumPracType.PracTypeCodeNetDriver.getValue())) {
                Assert.isTrue(true, "网约车从业资格为分开发证，请到网约资格管理做转入！");
            }
            //从业人员档案号编码规则采用的是系统管理中的机构编码规则中的从业人员档案编码类别；档案归管辖机构管，所以档案号生成规则取管辖机构的
            String number = pracApplyService.generateNumberByRecursive(pracApply.getOrgId(), CodeRuleService.PRAC_NO);
            log.info("管辖机构【{}】,生成的从业人员档案号：【{}】", pracApply.getOrgName(), number);
            pracApplyCertDTO.setArchiveNum(number);
            pracApplyCertDTO.setCertType(EnumPracCertType.Normal.getValue());
        }
        pracApplyCertMapStruct.updateToSource(pracApplyCertDTO, pracApplyCert);

        pracApplyCommonInfoDTO.setPracApplyCert(pracApplyCertDTO);
        //业务验证
        comBsVerifyService.bsCommonVerify(pracApplyCommonInfoDTO,
                EnumIndustryClass.Practitioner.getValue(), industryCode,
                pracApplyCommonInfoDTO.getApplyType(), pracApplyCommonInfoDTO.getOrgId());
        pracApplyCertService.updateById(pracApplyCert);

        //4.归档从业资格申请考试信息表
        //PracApplyExamDTO pracApplyExamDTO = pracApplyCommonInfoDTO.getPracApplyExam();
        //PracApplyExam pracApplyExam = pracApplyExamMapStruct.toSource(pracApplyExamDTO);
        //pracApplyExam.setModifyTime(new Date());
        //pracApplyExamService.update(pracApplyExam);

        //5.归档从业资格申请考试成绩表
        List<PracApplyExamResultDTO> pracApplyExamResults = pracApplyCommonInfoDTO.getPracApplyExamResults();
        if (EmptyUtil.isNotEmpty(pracApplyExamResults)) {
            List<PracApplyExamResult> list = new ArrayList<>();
            for (PracApplyExamResultDTO pracApplyExamResultDTO : pracApplyExamResults) {
                PracApplyExamResult pracApplyExamResult = pracApplyExamResultMapStruct.toSource(pracApplyExamResultDTO);
                pracApplyExamResult.setApplyId(pracApply.getApplyId());
                pracApplyExamResult.setExamType(1);
                pracApplyExamResult.setExamResult(1);
                pracApplyExamResult.setModifyTime(new Date());
                list.add(pracApplyExamResult);
            }
            pracApplyExamResultService.saveOrUpdateBatch(list);
        }

        //4.材料信息，先把之前提交的材料删除，以新提交的材料清单重新入库
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        pracApplyMaterialService.savePracApplyMaterial(pracApplyMaterials, pracApply.getApplyId());

        //5.邮寄信息，先把之前提交的删除，以新提交的数据重新入库
        //先删除
        spBusinessApplyRecipientService.remove(Wrappers.lambdaQuery(SPBusinessApplyRecipient.class).eq(SPBusinessApplyRecipient::getApplyId, applyId));
        if (Objects.equals(pracApplyDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            //归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.save(spBusinessApplyRecipient);
        }

        List<WorkflowVar> workflowVars = Lists.newArrayList();
        WorkflowProcessDataDTO workflowProcessDataDTO = pracApplyCommonInfoDTO.getProcessData();
        workflowService.submitWorkflow(workflowProcessDataDTO, workflowVars, WorkflowUtils.convertToWorkflowUser(userInfo));

        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //9.发布事件
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Accepted));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onlineAccept(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        log.info("{},网上申请受理提交的信息:PracApplyCommonInfoDTO={},UserInfo={}", businessName(), pracApplyCommonInfoDTO, userInfo);
        //企业端登记时没有这些信息
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAccepter())) {
            pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAccepterId())) {
            pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptOrgId())) {
            pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptOrgName())) {
            pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptDate())) {
            pracApplyCommonInfoDTO.setAcceptDate(new Date());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getOperator())) {
            pracApplyCommonInfoDTO.setOperator(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getTelephone())) {
            pracApplyCommonInfoDTO.setTelephone(userInfo.getMobile());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getMobile())) {
            pracApplyCommonInfoDTO.setMobile(userInfo.getMobile());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getDepId())) {
            pracApplyCommonInfoDTO.setDepId(userInfo.getDepId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getDepName())) {
            pracApplyCommonInfoDTO.setDepName(userInfo.getDepName());
        }
        pracApplyCommonInfoDTO.setCheckState(EnumWebsiteTwoCheckState.None.getValue());
        pracApplyCommonInfoDTO.setReasonType(EnumPracReasonType.None.getValue());
        pracApplyCommonInfoDTO.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        pracApplyCommonInfoDTO.setAcceptDate(new Date());
        pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        pracApplyCommonInfoDTO.setState(EnumApplyState.Accepted.getValue());
        pracApplyCommonInfoDTO.setDecisionResult(EnumDecisionResult.None.getValue());
        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        PracApplyTransferInDTO pracApplyTransferInDTO = pracApplyCommonInfoDTO.getPracApplyTransferIn();
        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();


        String applyId = pracApplyDTO.getApplyId();
        PracApply pracApply = pracApplyService.getById(applyId);
        Assert.notNull(pracApply, "Id为【" + applyId + "】的从业人员申请信息已不存在！");
        pracApplyMapStruct.updateToSource(pracApplyDTO, pracApply);
        pracApply.setModifier(userInfo.getUserName());
        pracApply.setModifyTime(new Date());
        pracApplyService.updateById(pracApply);

        //归档转入申请表
        PracApplyTransferIn pracApplyTransferIn = pracApplyTransferInService.getById(applyId);
        if (EmptyUtil.isEmpty(pracApplyTransferIn)) {
            pracApplyTransferIn = new PracApplyTransferIn();
        }
        pracApplyTransferInMapStruct.updateToSource(pracApplyTransferInDTO, pracApplyTransferIn);
        if (EmptyUtil.isEmpty(pracApplyTransferIn.getCreditLevel())) {
            pracApplyTransferIn.setCreditLevel(0);
        }
        if (EmptyUtil.isEmpty(pracApplyTransferIn.getEducState())) {
            pracApplyTransferIn.setEducState(EnumEducState.OnGOing.getValue());
        }
        if (EmptyUtil.isEmpty(pracApplyTransferIn.getEducClassHour())) {
            pracApplyTransferIn.setEducClassHour(0);
        }
        pracApplyTransferIn.setApplyId(applyId);
        pracApplyTransferInService.saveOrUpdate(pracApplyTransferIn);

        //2.归档从业资格申请基础表

        PracApplyBase pracApplyBase = pracApplyBaseService.getById(applyId);
        pracApplyBaseMapStruct.updateToSource(pracApplyBaseDTO, pracApplyBase);
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.updateById(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.归档从业资格申请基础表
        PracApplyCert pracApplyCert = pracApplyCertService.getById(applyId);
        if (EmptyUtil.isEmpty(pracApplyCert)) {
            pracApplyCert = new PracApplyCert();
        }
        String industryCode = "0";
        //查询网约车从业人员是否合并发证参数 取发证机构的机构参数 2023-05-24 已经与项目经历讨论确认
        SysOrganize issueOrganize = sysOrganizeService.getIssueOrganize(pracApply.getOrgId(),
                EnumIndustryClass.Practitioner.getValue());
        Assert.notNull(issueOrganize, "未计算得出发证机构信息！");
        boolean isAlone =
                sysBusiParamService.getBusiParamBoolValue(issueOrganize.getOrgId(), SysBusiParamService.PRAC_MEGER_CERT);
        if (isAlone && pracApply.getPracTypeCodes().equals(EnumPracType.PracTypeCodeNetDriver.getValue())) {
            //单独发证
            pracApplyCertDTO.setCertType(EnumPracCertType.NetDriver.getValue());
            industryCode = EnumPracType.PracTypeCodeNetDriver.getValue();
        } else {
            //转入增类时从业资格可选择多个，这里有可能同时选了网约资格和其他资格，需要结合是否合并发证作判断
            if (isAlone && pracApply.getPracTypeCodes().contains(EnumPracType.PracTypeCodeNetDriver.getValue())) {
                Assert.isTrue(true, "网约车从业资格为分开发证，请到网约资格管理做转入！");
            }
            pracApplyCertDTO.setCertType(EnumPracCertType.Normal.getValue());

        }
        //默认发证日期是受理当天，制证时再更新，有效期起止，初领日期按照用户提交，
        if (EmptyUtil.isEmpty(pracApplyCertDTO.getPracCertStartDate())) {
            pracApplyCertDTO.setPracCertStartDate(new Date());
            Date pracCertEndDate = pracApplyService.calcPracCertEndDate(pracApply, pracApplyBase, pracApplyCertDTO.getPracCertStartDate());
            Assert.notNull(pracCertEndDate, "未能计算得到新的有效期止时间！");
            pracApplyCertDTO.setPracCertEndDate(pracCertEndDate);
            pracApplyCertDTO.setPracCertFirstDate(new Date());
            pracApplyCertDTO.setPracCertIssueDate(new Date());
        }
        pracApplyCertDTO.setIssueOrgId(issueOrganize.getOrgId());
        pracApplyCertDTO.setIssueOrgName(issueOrganize.getOrgName());
        pracApplyCertDTO.setPracTypeCodes(pracApply.getPracTypeCodes());
        pracApplyCertDTO.setPracTypeNames(pracApply.getPracTypeNames());
        pracApplyCertDTO.setCertNum(pracApply.getIdCertNum());
        pracApplyCertDTO.setPracCertNum(pracApply.getIdCertNum());
        pracApplyCertMapStruct.updateToSource(pracApplyCertDTO, pracApplyCert);
        pracApplyCert.setModifyTime(new Date());
        pracApplyCert.setApplyId(applyId);
        pracApplyCertService.saveOrUpdate(pracApplyCert);
        pracApplyCommonInfoDTO.setPracApplyCert(pracApplyCertDTO);
        //业务验证
        comBsVerifyService.bsCommonVerify(pracApplyCommonInfoDTO,
                EnumIndustryClass.Practitioner.getValue(), industryCode,
                pracApplyCommonInfoDTO.getApplyType(), pracApplyCommonInfoDTO.getOrgId());

        //4.材料信息，先把之前提交的材料删除，以新提交的材料清单重新入库
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        pracApplyMaterialService.savePracApplyMaterial(pracApplyMaterials, pracApply.getApplyId());

        //5.归档从业资格申请考试成绩表
        List<PracApplyExamResultDTO> pracApplyExamResults = pracApplyCommonInfoDTO.getPracApplyExamResults();
        if (EmptyUtil.isNotEmpty(pracApplyExamResults)) {
            List<PracApplyExamResult> list = new ArrayList<>();
            for (PracApplyExamResultDTO pracApplyExamResultDTO : pracApplyExamResults) {
                PracApplyExamResult pracApplyExamResult = pracApplyExamResultMapStruct.toSource(pracApplyExamResultDTO);
                pracApplyExamResult.setApplyId(pracApply.getApplyId());
                pracApplyExamResult.setExamType(1);
                pracApplyExamResult.setExamResult(1);
                pracApplyExamResult.setModifyTime(new Date());
                list.add(pracApplyExamResult);
            }
            pracApplyExamResultService.saveOrUpdateBatch(list);
        }

        // 5.归档申请人邮寄信息
        if (Objects.equals(pracApplyDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.saveOrUpdate(spBusinessApplyRecipient);
        }

        // 案号生成
        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //7.工作流
        pracApplyCommonInfoDTO.getProcessData().setProcessId("00106");
        StringBuilder bizObjectName = new StringBuilder(pracApplyDTO.getPracName());
        if (EmptyUtil.isNotEmpty(pracApply.getIdCertNum())) {
            bizObjectName.append(MessageFormat.format("（身份证：{0}）", pracApplyDTO.getIdCertNum()));
        }
        if (EmptyUtil.isNotEmpty(pracApplyCert.getArchiveNum())) {
            bizObjectName.append(MessageFormat.format("（档案号：{0}）", pracApplyCert.getArchiveNum()));
        }
        workflowService.startWorkflow(WorkflowUtils.convertToWorkflowUser(userInfo), pracApply.getApplyId(), pracApply.getBusinessNo(), bizObjectName, pracApplyCommonInfoDTO.getProcessData());

        //9.发布事件
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Created));
        try {
            // 睡一会
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Accepted));
    }

    /**
     * 转入
     *
     * @param pracApply     进行申请
     * @param pracApplyBase 进行申请基地
     * @param pracApplyCert 进行申请cert
     * @param userInfo      用户信息
     * <AUTHOR>
     * @since 2023/07/20 11:40:24
     */
    @Override
    @SuppressWarnings({"AlibabaMethodTooLong", "AlibabaRemoveCommentedCode", "DuplicatedCode"})
    public void archive(PracApply pracApply, PracApplyBase pracApplyBase, PracApplyCert pracApplyCert, UserInfo userInfo) {
        //根据身份证号查询本机构是否已有档案
        Practitioner practitioner = practitionerService.getOne(Wrappers.<Practitioner>lambdaQuery().eq(Practitioner::getIdCertNum,
                pracApply.getIdCertNum()).eq(Practitioner::getAreaCode,
                userInfo.getAreaCode()), false);
        log.info("转入时查询人员档案是否存在{}", EmptyUtil.isNotEmpty(practitioner));
        if (EmptyUtil.isNotEmpty(practitioner)) {
            log.info("转入时已有人员档案{}", JSON.toJSONString(practitioner));
            //为避免重复拼接，只拼接申请的从业资格和档案信息中从业资格的差值
            if (EmptyUtil.isEmpty(practitioner.getPracTypeCodes())) {
                practitioner.setPracTypeCodes(pracApply.getPracTypeCodes());
                practitioner.setPracTypeNames(pracApply.getPracTypeNames());
            } else {
                String shouldAppendCodes = String.join(",", CollectionUtil.subtractToList(Arrays.asList(pracApply.getPracTypeCodes().split(",")), Arrays.asList(practitioner.getPracTypeCodes().split(","))));
                String shouldAppendNames = String.join(",", CollectionUtil.subtractToList(Arrays.asList(pracApply.getPracTypeNames().split(",")), Arrays.asList(practitioner.getPracTypeNames().split(","))));
                practitioner.setPracTypeCodes(practitioner.getPracTypeCodes() + "," + shouldAppendCodes);
                practitioner.setPracTypeNames(practitioner.getPracTypeNames() + "," + shouldAppendNames);
            }
        } else {
            practitioner = new Practitioner();
            practitioner.setPracTypeNames(pracApply.getPracTypeNames());
            practitioner.setPracTypeCodes(pracApply.getPracTypeCodes());
            practitioner.setCreateTime(new Date());
            practitioner.setCreator(userInfo.getUserName());
            practitioner.setArchiveNum(pracApplyCert.getArchiveNum());
        }
        practitioner.setPracName(pracApply.getPracName());
        practitioner.setSex(pracApplyBase.getSex());
        practitioner.setBirthday(pracApplyBase.getBirthday());
        practitioner.setNationality(pracApplyBase.getNationality());
        practitioner.setIdCertType(pracApply.getIdCertType());
        practitioner.setIdCertNum(pracApply.getIdCertNum());
        practitioner.setCounty(pracApplyBase.getCounty());
        if (EmptyUtil.isNotEmpty(pracApplyBase.getCountyName())) {
            practitioner.setCountyName(pracApplyBase.getCountyName());
        }
        practitioner.setPhoneNum(pracApplyBase.getPhoneNum());
        practitioner.setMobile(pracApplyBase.getMobile());
        practitioner.setIdCertAddress(pracApplyBase.getIdCertAddress());
        practitioner.setAddress(pracApplyBase.getAddress());
        practitioner.setEmail(pracApplyBase.getEmail());
        practitioner.setPostcode(pracApplyBase.getPostcode());
        practitioner.setEducation(pracApplyBase.getEducation());
        practitioner.setTechnicalPost(pracApplyBase.getTechnicalPost());
        practitioner.setHealthState(pracApplyBase.getHealthState());
        practitioner.setDrivingLicense(pracApplyBase.getDrivingLicense());
        practitioner.setDrivingVehicle(pracApplyBase.getDrivingVehicle());
        practitioner.setDrivLiceFirstDate(pracApplyBase.getDrivLiceFirstDate());
        practitioner.setDrivLiceStartDate(pracApplyBase.getDrivLiceStartDate());
        practitioner.setDrivLiceEndDate(pracApplyBase.getDrivLiceEndDate());
        practitioner.setDrivLicePeriod(pracApplyBase.getDrivLicePeriod());
        practitioner.setDrivLiceOrg(pracApplyBase.getDrivLiceOrg());
        practitioner.setDrivArchiveNum(pracApplyBase.getDrivArchiveNum());

        practitioner.setPhotoPath(pracApplyBase.getPhotoPath());
        practitioner.setTeachVehicleFirstDate(pracApplyBase.getTeachVehicleFirstDate());
        practitioner.setSafeDriving(pracApplyBase.getSafeDriving());
        practitioner.setAreaCode(pracApply.getAreaCode());
        practitioner.setOrgId(pracApply.getOrgId());
        practitioner.setComments(pracApply.getComments());
        practitioner.setOnJob(EnumPracIsOnJob.NotOnJob.getValue());
        practitioner.setMaritalStatus(pracApplyBase.getMaritalStatus());
        practitioner.setForeignLanguageAbility(pracApplyBase.getForeignLanguageAbility());
        practitioner.setIfLocalPeople(pracApplyBase.getIfLocalPeople());
        practitioner.setCitizenship(pracApplyBase.getCitizenship());
        practitioner.setPartyMemberStatus(pracApplyBase.getPartyMemberStatus());
        practitioner.setVeteransStatus(pracApplyBase.getVeteransStatus());
        practitioner.setModifier(userInfo.getUserName());
        practitioner.setModifyTime(new Date());
        practitionerService.saveOrUpdate(practitioner);

        LambdaUpdateWrapper<PracApply> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(PracApply::getPracId, practitioner.getPracId());
        lambdaUpdateWrapper.eq(PracApply::getApplyId, pracApply.getApplyId());
        pracApplyService.update(lambdaUpdateWrapper);

        // 韩总：在初领归档和转入的时候，如果发现有同类别无效的证件，把证件的【类别】改成 9 (无效证件) 2023/10/25
        // 在任何时候，无论是否有效，同类别的证件只有一个
        LambdaUpdateWrapper<PracCertificate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PracCertificate::getCertType, EnumPracCertType.Invalid.getValue());
        updateWrapper.eq(PracCertificate::getPracId, practitioner.getPracId());
        updateWrapper.eq(PracCertificate::getCertType, pracApplyCert.getCertType());// 同类别
        updateWrapper.eq(PracCertificate::getCertState, EnumCertificateState.Invalid.getValue());// 无效
        pracCertificateService.update(updateWrapper);

        //查询是否已有证件
        PracCertificate pracCertificate = pracCertificateService.getOne(Wrappers.lambdaQuery(PracCertificate.class)
                .eq(PracCertificate::getPracId, practitioner.getPracId())
                .eq(PracCertificate::getCertType, pracApplyCert.getCertType())
                .eq(PracCertificate::getCertState, EnumCertificateState.Valid.getValue())
        );
        // update by hxq 2023-08-11跟崔确认，不管人员是否已有证件，初领日期，证件有效期起，证件有效期止都按照用户提交值，发证日期为当天。
        if (EmptyUtil.isEmpty(pracCertificate)) {
            //考试中心同步过来的数据，可能没有初领日期，默认赋值为当天
            pracCertificate = new PracCertificate();
            pracCertificate.setPracTypeCodes(pracApply.getPracTypeCodes());
            pracCertificate.setPracTypeNames(pracApply.getPracTypeNames());
            if (EmptyUtil.isNotEmpty(pracApplyCert.getPracCertFirstDate()) && !pracApply.getSource().equals(EnumApplySource.Other.getValue())) {
                pracCertificate.setPracCertFirstDate(pracApplyCert.getPracCertFirstDate());
            } else {
                //考试系统同步过来的
                pracCertificate.setPracCertFirstDate(new Date());
            }
            if (EmptyUtil.isNotEmpty(pracApplyCert.getPracCertStartDate()) && !pracApply.getSource().equals(EnumApplySource.Other.getValue())) {
                pracCertificate.setPracCertStartDate(pracApplyCert.getPracCertStartDate());
            } else {
                //考试系统同步过来的
                pracCertificate.setPracCertStartDate(new Date());
            }
            if (EmptyUtil.isNotEmpty(pracApplyCert.getPracCertEndDate()) && !pracApply.getSource().equals(EnumApplySource.Other.getValue())) {
                pracCertificate.setPracCertEndDate(pracApplyCert.getPracCertEndDate());
            } else {
                //考试系统同步过来的
                Date pracCertEndDate = pracApplyService.calcPracCertEndDate(pracApply, pracApplyBase, pracCertificate.getPracCertStartDate());
                Assert.notNull(pracCertEndDate, "未能计算得到新的有效期止时间！");
                pracCertificate.setPracCertEndDate(pracCertEndDate);
            }
            //考试系统同步的申请档案号为空
            if (EmptyUtil.isEmpty(pracApplyCert.getArchiveNum())) {
                String number = "";
                if (pracApplyCert.getCertType().intValue() == EnumPracCertType.NetDriver.getValue()) {
                    //从业人员档案号编码规则采用的是系统管理中的机构编码规则中的从业人员档案编码类别；档案归管辖机构管，所以档案号生成规则取管辖机构的
                    number = pracApplyService.generateNumberByRecursive(pracApply.getOrgId(), CodeRuleService.PRAC_NO_AP_TAXI);
                    log.info("管辖机构【{}】,生成的从业人员档案号：【{}】", pracApply.getOrgName(), number);
                } else if (pracApplyCert.getCertType().intValue() == EnumPracCertType.Normal.getValue()) {
                    //从业人员档案号编码规则采用的是系统管理中的机构编码规则中的从业人员档案编码类别；档案归管辖机构管，所以档案号生成规则取管辖机构的
                    number = pracApplyService.generateNumberByRecursive(pracApply.getOrgId(), CodeRuleService.PRAC_NO);
                    log.info("管辖机构【{}】,生成的从业人员档案号：【{}】", pracApply.getOrgName(), number);
                }
                pracCertificate.setArchiveNum(number);
                pracApplyCert.setArchiveNum(number);
            } else {
                pracCertificate.setArchiveNum(pracApplyCert.getArchiveNum());
            }
            //新生成证件表，如果档案表已有值，则档案表的档案号不需要更新，如果档案表没有值，则档案表的档案号需要新赋值(档案表的档案编号不可为空)
            if (EmptyUtil.isEmpty(practitioner.getArchiveNum())) {
                practitioner.setArchiveNum(pracCertificate.getArchiveNum());
            }

            practitionerService.update(practitioner);
        } else {
            //已有证件，如果初领日期没填，就不更新
            pracCertificate.setPracTypeCodes(pracCertificate.getPracTypeCodes() + "," + pracApply.getPracTypeCodes());
            pracCertificate.setPracTypeNames(pracCertificate.getPracTypeNames() + "," + pracApply.getPracTypeNames());
            if (EmptyUtil.isNotEmpty(pracApplyCert.getPracCertFirstDate())) {
                pracCertificate.setPracCertFirstDate(pracApplyCert.getPracCertFirstDate());
            }
            if (EmptyUtil.isNotEmpty(pracApplyCert.getPracCertStartDate())) {
                pracCertificate.setPracCertStartDate(pracApplyCert.getPracCertStartDate());
            }
            if (EmptyUtil.isNotEmpty(pracApplyCert.getPracCertEndDate())) {
                pracCertificate.setPracCertEndDate(pracApplyCert.getPracCertEndDate());
            }
        }
        //2.2 归档从业人员资格证信息
        pracCertificate.setCounty(pracApplyBase.getCounty());
        pracCertificate.setCertNum(pracApplyCert.getCertNum());
        pracCertificate.setCertType(pracApplyCert.getCertType());
        pracCertificate.setIssueOrgName(pracApplyCert.getIssueOrgName());
        pracCertificate.setIssueOrgId(pracApplyCert.getIssueOrgId());
        pracCertificate.setPracCertNum(pracApplyCert.getPracCertNum());
        pracCertificate.setTeachingVehicles(pracApplyBase.getTeachingVehicles());
        pracCertificate.setPracCertIssueDate(new Date());

        pracCertificate.setCertState(EnumCertificateState.Valid.getValue());
        pracCertificate.setPracState(EnumPractitionerState.Waiting.getValue());
        pracCertificate.setOrgId(pracApply.getOrgId());
        pracCertificate.setAreaCode(pracApply.getAreaCode());
        pracCertificate.setPracCertPhotoPath(pracApplyCert.getPracCertPhotoPath());
        pracCertificate.setVerifyCode(pracApplyCert.getVerifyCode());
        pracCertificate.setICCardNum(pracApplyCert.getIcCardNum());
        pracCertificate.setCardTime(pracApplyCert.getCardTime());
        pracCertificate.setPracId(practitioner.getPracId());
        pracCertificateService.saveOrUpdate(pracCertificate);

        LambdaUpdateWrapper<PracApplyCert> lambdaPracApplyCertWrapper = new LambdaUpdateWrapper<>();
        lambdaPracApplyCertWrapper.set(PracApplyCert::getPracCertIssueDate, pracCertificate.getPracCertIssueDate());
        lambdaPracApplyCertWrapper.set(PracApplyCert::getPracCertStartDate, pracCertificate.getPracCertStartDate());
        lambdaPracApplyCertWrapper.set(PracApplyCert::getPracCertEndDate, pracCertificate.getPracCertEndDate());
        lambdaPracApplyCertWrapper.set(PracApplyCert::getPracCertFirstDate, pracCertificate.getPracCertFirstDate());
        lambdaPracApplyCertWrapper.set(PracApplyCert::getArchiveNum, pracCertificate.getArchiveNum());
        lambdaPracApplyCertWrapper.eq(PracApplyCert::getApplyId, pracApply.getApplyId());
        pracApplyCertService.update(lambdaPracApplyCertWrapper);

        //3.归档从业人员资格信息-从业资格代码明细，记录每个资格
        String pracTypeCodes = pracApply.getPracTypeCodes();
        String pracTypeNames = pracApply.getPracTypeNames();
        String[] pracTypeCodeSplits = pracTypeCodes.split(",");
        String[] pracTypeNamesSplits = pracTypeNames.split(",");
        Assert.isTrue(pracTypeCodeSplits.length == pracTypeNamesSplits.length, "提交的从业资格代码和名称的长度不一致！");

        List<PracQualification> pracQualifications = new ArrayList<>();
        for (String pracTypeCode : pracTypeCodeSplits) {
            Optional<EnumPracType> pracTypeOptional = EnumPracType.getByValue(pracTypeCode);
            if (pracTypeOptional.isPresent()) {
                PracQualification pracQualification = new PracQualification();
                pracQualification.setPracTypeCode(pracTypeCode);
                pracQualification.setPracTypeName(pracTypeOptional.get().getDescription());
                pracQualification.setPracId(practitioner.getPracId());
                pracQualification.setPracCertId(pracCertificate.getPracCertId());
                pracQualification.setFirstDate(pracCertificate.getPracCertFirstDate());
                pracQualification.setIssueDate(pracCertificate.getPracCertIssueDate());
                pracQualification.setCertNum(pracCertificate.getCertNum());
                pracQualifications.add(pracQualification);
            }
        }
        pracQualificationService.saveBatch(pracQualifications);

        //重置从业人员基本信息中从业资格类型名称和代码
        resetPracTypeNameAndCode(practitioner.getPracId());
    }
}
