package com.guanwei.tmis.practitioner.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.core.utils.result.R;
import com.guanwei.tmis.common.enums.*;
import com.guanwei.tmis.common.service.CodeRuleService;
import com.guanwei.tmis.common.service.SysBusiParamService;
import com.guanwei.tmis.practitioner.common.dto.*;
import com.guanwei.tmis.practitioner.common.dto.mapstruct.*;
import com.guanwei.tmis.practitioner.common.entity.*;
import com.guanwei.tmis.practitioner.common.enums.EnumPracRegisterApTaxiTempState;
import com.guanwei.tmis.practitioner.common.mapper.PracCertificateMapper;
import com.guanwei.tmis.practitioner.common.mapper.PracRegisterApTaxiTempMapper;
import com.guanwei.tmis.practitioner.common.mapper.PracRegisterMapper;
import com.guanwei.tmis.practitioner.common.mapper.PractitionerMapper;
import com.guanwei.tmis.practitioner.common.service.*;
import com.guanwei.tmis.practitioner.service.PracRegisterImportService;
import com.guanwei.toi.feign.vehicle.ToiVehicleFeignClient;
import com.guanwei.toi.model.VehicleInfoModel;
import com.guanwei.toi.model.VehicleNetTaxiBindOwnerModel;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.text.MessageFormat;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class PracRegisterImportServiceImpl implements PracRegisterImportService {

    private final PracApplyService pracApplyService;

    private final PracApplyBaseService pracApplyBaseService;

    private final PracApplyCertService pracApplyCertService;

    private final PracApplyRegisterService pracApplyRegisterService;

    private final PracCertificateService pracCertificateService;

    private final SysBusiParamService sysBusiParamService;

    private final CodeRuleService codeRuleService;

    private final PractitionerMapper practitionerMapper;

    private final PracCertificateMapper pracCertificateMapper;

    private final PracRegisterMapper pracRegisterMapper;

    private final PracRegisterApTaxiTempMapper pracRegisterApTaxiTempMapper;

    private final ToiVehicleFeignClient toiVehicleFeignClient;

    private final PracApplyMapStruct pracApplyMapStruct;

    private final PracApplyBaseMapStruct pracApplyBaseMapStruct;

    private final PracApplyCertMapStruct pracApplyCertMapStruct;

    private final PracRegisterMapStruct pracRegisterMapStruct;

    private final PracRegisterApTaxiTempMapStruct pracRegisterApTaxiTempMapStruct;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void validateAndSave(PracRegisterApTaxiTempMqDTO dto) {

        PracRegisterApTaxiTempDTO temp = pracRegisterApTaxiTempMapStruct.toTempDTO(dto);

        Assert.notNull(temp, "数据不可为空");
        StringBuilder strDescribe = new StringBuilder();
        // 验证注册备案信息
        verifyHadRegister(temp, strDescribe);
        // 验证导入的人员信息
        verifyPractitioner(temp, strDescribe);
        // 验证车辆
        verifyVehicle(temp, strDescribe);
        // 判断是校验是否通过
        isCheckPass(temp, strDescribe);
        // 保存数据
        // 是否有未审核通过的数据 有则更新 无则新增
        List<Integer> states = new ArrayList<>();
        states.add(EnumPracRegisterApTaxiTempState.TRANS_CHECK_PASS.getValue());
        states.add(EnumPracRegisterApTaxiTempState.POLICE_CHECK_PASS.getValue());
        LambdaQueryWrapper<PracRegisterApTaxiTemp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PracRegisterApTaxiTemp::getPracCertNum, temp.getPracCertNum());
        wrapper.eq(PracRegisterApTaxiTemp::getOwnerLicenseNum, temp.getOwnerLicenseNum());
        wrapper.notIn(PracRegisterApTaxiTemp::getState, states);
        wrapper.orderByDesc(PracRegisterApTaxiTemp::getCreateTime);
        PracRegisterApTaxiTemp tempOld = pracRegisterApTaxiTempMapper.selectOne(wrapper, false);
        if (EmptyUtil.isNotEmpty(tempOld)) {
            LambdaUpdateWrapper<PracRegisterApTaxiTemp> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PracRegisterApTaxiTemp::getTempId, tempOld.getTempId());
            updateWrapper.set(PracRegisterApTaxiTemp::getPracName, temp.getPracName());
            updateWrapper.set(PracRegisterApTaxiTemp::getSex, temp.getSex());
            updateWrapper.set(PracRegisterApTaxiTemp::getAge, temp.getAge());
            updateWrapper.set(PracRegisterApTaxiTemp::getDrivLiceFirstDate, temp.getDrivLiceFirstDate());
            updateWrapper.set(PracRegisterApTaxiTemp::getPracTypeCode, temp.getPracTypeCode());
            updateWrapper.set(PracRegisterApTaxiTemp::getOwnerName, temp.getOwnerName());
            updateWrapper.set(PracRegisterApTaxiTemp::getOwnerLicenseNum, temp.getOwnerLicenseNum());
            updateWrapper.set(PracRegisterApTaxiTemp::getVehicleNum, temp.getVehicleNum());
            updateWrapper.set(PracRegisterApTaxiTemp::getPlateColor, temp.getPlateColor());
            updateWrapper.set(PracRegisterApTaxiTemp::getLaborStartDate, temp.getLaborStartDate());
            updateWrapper.set(PracRegisterApTaxiTemp::getLaborEndDate, temp.getLaborEndDate());
            updateWrapper.set(PracRegisterApTaxiTemp::getModifyTime, temp.getModifyTime());
            updateWrapper.set(PracRegisterApTaxiTemp::getModifier, temp.getModifier());
            updateWrapper.set(PracRegisterApTaxiTemp::getOrgId, temp.getOrgId());
            updateWrapper.set(PracRegisterApTaxiTemp::getState, temp.getState());
            updateWrapper.set(PracRegisterApTaxiTemp::getDescribe, temp.getDescribe());
            pracRegisterApTaxiTempMapper.update(updateWrapper);
        } else {
            PracRegisterApTaxiTemp tempNew = pracRegisterApTaxiTempMapStruct.toSource(temp);
            pracRegisterApTaxiTempMapper.insert(tempNew);
        }
        // 注册信息通过校验，归档
        if (EmptyUtil.isNotEmpty(temp.getState()) && temp.getState().equals(EnumPracRegisterApTaxiTempState.TRANS_CHECK_PASS.getValue())) {
            // 生成 pracApply
            String pracId = temp.getPracId();
            String pracCertId = temp.getPracCertId();
            Practitioner practitioner = practitionerMapper.selectById(pracId);
            PracCertificate pracCertificate = pracCertificateMapper.selectById(pracCertId);

            Assert.notNull(practitioner, "Id为【" + pracId + "】的从业人员信息不存在！");
            Assert.notNull(pracCertificate, "Id为【" + pracCertId + "】的从业人员证件信息不存在！");
            PracApplyDTO pracApplyDTO = pracApplyMapStruct.toPracApplyDto(practitioner);
            pracApplyDTO.setApplyType(EnumPracApplyType.Register.getValue());
            pracApplyDTO.setPracTypeCodes(pracCertificate.getPracTypeCodes());
            pracApplyDTO.setPracTypeNames(pracCertificate.getPracTypeNames());
            pracApplyDTO.setIsMail(0);
            pracApplyDTO.setApplyId(IdUtil.getSnowflakeNextIdStr());
            pracApplyDTO.setApplyDate(new Date());
            pracApplyDTO.setBusinessName("网约车驾驶员注册登记");

            PracApply pracApply = pracApplyMapStruct.toSource(pracApplyDTO);
            pracApply.setReasonType(EnumPracReasonType.None.getValue());
            pracApply.setSource(EnumApplySource.Auto.getValue());
            pracApply.setIdCertType(EnumIdCertificateType.IdCard.getValue());
            pracApply.setIsMail(EnumYesOrNot.Not.getValue());
            pracApply.setBusinessNo(codeRuleService.generateOrgNumber(pracApply.getOrgId(), CodeRuleService.CASE_NO_PRAC, true));
            pracApply.setDecisionResult(EnumDecisionResult.Allowed.getValue());
            pracApply.setAcceptResult(EnumAcceptResult.Accepted.getValue());
            pracApply.setAccepter("系统自动");
            pracApply.setAccepterId("系统自动");
            pracApply.setAcceptDate(new Date());
            pracApply.setAcceptOrgId(pracApply.getOrgId());
            pracApply.setAcceptOrgName(pracApply.getOrgName());
            pracApply.setState(EnumApplyState.Completed.getValue());
            pracApply.setCheckState(EnumCheckState.Passed.getValue());
            pracApply.setChecker("系统自动");
            pracApply.setCheckTime(new Date());
            pracApply.setCreator("系统自动");
            pracApply.setCreateTime(new Date());
            pracApply.setModifier("系统自动");
            pracApply.setModifyTime(new Date());
            pracApplyService.save(pracApply);

            // 生成 pracApplyBase
            PracApplyBaseDTO pracApplyBaseDTO = pracApplyMapStruct.toPracApplyBaseDto(practitioner, pracCertificate);
            PracApplyBase pracApplyBase = pracApplyBaseMapStruct.toSource(pracApplyBaseDTO);
            pracApplyBase.setApplyId(pracApply.getApplyId());
            pracApplyBase.setCreateTime(new Date());
            pracApplyBase.setModifyTime(new Date());
            pracApplyBaseService.save(pracApplyBase);
            pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

            // 生成 pracApplyCert
            PracApplyCertDTO pracApplyCertDTO = pracApplyMapStruct.toPracApplyCertDto(pracCertificate);
            PracApplyCert pracApplyCert = pracApplyCertMapStruct.toSource(pracApplyCertDTO);

            pracApplyCert.setPracTypeCodes(pracApply.getPracTypeCodes());
            pracApplyCert.setPracTypeNames(pracApply.getPracTypeNames());
            pracApplyCert.setCertNum(pracApply.getIdCertNum());
            pracApplyCert.setPracCertNum(pracApply.getIdCertNum());
            pracApplyCert.setApplyId(pracApply.getApplyId());
            pracApplyCert.setModifyTime(new Date());
            pracApplyCertService.save(pracApplyCert);

            // 生成 pracApplyRegister
            PracApplyRegister pracApplyRegister = getPracApplyRegister(temp);
            pracApplyRegister.setApplyId(pracApply.getApplyId());
            pracApplyRegisterService.save(pracApplyRegister);

            // 生成 pracRegister
            // 归档：更新从业人员资格证状态
            LambdaUpdateWrapper<PracCertificate> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(PracCertificate::getPracCertId, pracApplyRegister.getPracCertId());
            lambdaUpdateWrapper.set(PracCertificate::getPracState, EnumPractitionerState.Working.getValue());
            pracCertificateMapper.update(lambdaUpdateWrapper);

            // 归档：更新从业人员在岗状态
            LambdaUpdateWrapper<Practitioner> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Practitioner::getPracId, pracApplyRegister.getPracId());
            updateWrapper.set(Practitioner::getOnJob, EnumPracIsOnJob.onJob.getValue());
            practitionerMapper.update(updateWrapper);

            //归档：人员备案报表
            PracRegister pracRegister = new PracRegister();
            pracRegisterMapStruct.updateToSource(practitioner, pracRegister);
            pracRegisterMapStruct.updateToSource(pracApplyRegister, pracRegister);
            pracRegister.setMobile(practitioner.getMobile());
            pracRegister.setRegisterDate(new Date());

            pracRegister.setCreator("系统导入");
            pracRegister.setModifier("系统导入");
            pracRegister.setOrgId(temp.getOrgId());
            pracRegister.setPracCertId(pracCertificate.getPracCertId());
            pracRegister.setPracCertEndDate(pracCertificate.getPracCertEndDate());

            //1.正常 2.任务中
            pracRegister.setState(EnumPracRegisterState.Normal.getValue());
            pracRegister.setCreateTime(new Date());
            pracRegister.setModifyTime(new Date());
            pracRegisterMapper.insert(pracRegister);
        }
    }

    /**
     * 验证从业人员信息
     *
     * @param temp        excel中从业人员信息
     * @param strDescribe 描述
     */
    private void verifyPractitioner(PracRegisterApTaxiTempDTO temp, StringBuilder strDescribe) {

        Boolean isAlone = sysBusiParamService.isAloneCertificate(temp.getOrgId());

        PracCertificateDTO pracCertificateDTO;
        String pracTypeCodes = EnumPracType.TaxiTransport.getValue() + "," + EnumPracType.PracTypeCodeNetDriver.getValue();
        if (isAlone) {
            pracCertificateDTO = pracCertificateService.getPracRegisterImportCert(temp.getPracCertNum(), EnumPracCertType.NetDriver.getValue(), pracTypeCodes, temp.getAreaCode().substring(0, 4));
            if (EmptyUtil.isEmpty(pracCertificateDTO)) {
                pracCertificateDTO = pracCertificateService.getPracRegisterImportCert(temp.getPracCertNum(), EnumPracCertType.Normal.getValue(), pracTypeCodes, temp.getAreaCode().substring(0, 4));
            }
        } else {
            pracCertificateDTO = pracCertificateService.getPracRegisterImportCert(temp.getPracCertNum(), EnumPracCertType.Normal.getValue(), pracTypeCodes, temp.getAreaCode().substring(0, 4));
        }

        if (EmptyUtil.isEmpty(pracCertificateDTO)) {
            strDescribe.append("根据从业资格证号【").append(temp.getPracCertNum()).append("】及业户所属地区编码查询，系统中未查询到有效的网约或巡游资格信息。");
        } else {
            Date pracCertEndDate = pracCertificateDTO.getPracCertEndDate();
            DateTime now = DateUtil.date();
            if (now.isAfter(pracCertEndDate)) {
                strDescribe.append("从业人员【").append(pracCertificateDTO.getPracCertNum()).append("】").append("从业资格证已过期，请尽快换证！");
            }
            temp.setPracCertId(pracCertificateDTO.getPracCertId());
            temp.setPracId(pracCertificateDTO.getPracId());
            Practitioner practitioner = practitionerMapper.selectById(pracCertificateDTO.getPracId());
            if (EmptyUtil.isNotEmpty(practitioner)) {
                if (!Objects.equals(practitioner.getPracName(), temp.getPracName())) {
                    strDescribe.append("导入的人员[姓名]和系统中的不一致，系统中的是[").append(practitioner.getPracName()).append("]；");
                }
                if (!Objects.equals(practitioner.getSex(), temp.getSex())) {
                    Optional<EnumSex> sexOptional = EnumSex.getByValue(practitioner.getSex());
                    sexOptional.ifPresent(enumSex -> strDescribe.append("导入的人员[性别]和系统中的不一致，系统中的是[").append(enumSex.getDescription()).append("]；"));
                }
            } else {
                strDescribe.append("未查询到从业人员信息。");
            }
        }
    }

    /**
     * 验证从业人员信息
     *
     * @param temp        excel中从业人员信息
     * @param strDescribe 描述
     */
    private void verifyVehicle(PracRegisterApTaxiTempDTO temp, StringBuilder strDescribe) {

        if (EmptyUtil.isNotEmpty(temp.getOrgId())) {
            boolean bool = sysBusiParamService.getBusiParamBoolValue(temp.getOrgId(), SysBusiParamService.PARAM_CODE_PR0047);

            // 如果需要验证
            if (bool) {
                if (EmptyUtil.isNotEmpty(temp.getOwnerId())) {
                    boolean flag = false;
                    R<VehicleInfoModel> vehicleRes = toiVehicleFeignClient.getVehicleByVehicleNumAndPlateColor(temp.getVehicleNum(), Integer.valueOf(temp.getPlateColor()));
                    if (vehicleRes.isSuccess()) {
                        VehicleInfoModel vehicle = vehicleRes.getData();
                        if (EmptyUtil.isNotEmpty(vehicle)) {
                            R<List<VehicleNetTaxiBindOwnerModel>> ownersRes = toiVehicleFeignClient.getNetTaxiOwnerIdByVehicleId(vehicle.getVehicleId());
                            if (ownersRes.isSuccess()) {
                                List<VehicleNetTaxiBindOwnerModel> owners = ownersRes.getData();
                                if (EmptyUtil.isNotEmpty(owners)) {
                                    for (VehicleNetTaxiBindOwnerModel owner : owners) {
                                        String ownerId = owner.getOwnerId();
                                        if (Objects.equals(ownerId, temp.getOwnerId())) {
                                            flag = true;
                                        }
                                    }
                                    if (!flag) {
                                        strDescribe.append("车辆[").append(temp.getVehicleNum()).append("]不属于业户[").append(temp.getOwnerName()).append("]的网约车注册车辆；");
                                    }
                                } else {
                                    strDescribe.append("未查询到网约车车辆绑定业户信息；");
                                }
                            } else {
                                strDescribe.append("未查询到网约车车辆绑定业户信息，报错：").append(ownersRes.getMsg()).append("；");
                            }
                        } else {
                            strDescribe.append("未查询到网约车车辆信息；");
                        }
                    } else {
                        strDescribe.append("未查询到网约车车辆信息，报错:").append(vehicleRes.getMsg()).append("；");
                    }
                }
            }
        }
    }

    /**
     * 判断校验是否通过
     *
     * @param temp        excel中从业人员信息
     * @param strDescribe 描述
     */
    private void isCheckPass(PracRegisterApTaxiTempDTO temp, StringBuilder strDescribe) {
        if (StrUtil.isBlank(strDescribe.toString())) {
            temp.setState(EnumPracRegisterApTaxiTempState.TRANS_CHECK_PASS.getValue());
            temp.setDescribe("");
        } else {
            if (strDescribe.toString().equals("此驾驶员已在当前业户注册")) {
                temp.setState(EnumPracRegisterApTaxiTempState.TRANS_CHECK_PASS.getValue());
                temp.setDescribe(strDescribe.toString().length() > 100
                        ? StrUtil.sub(strDescribe.toString(), 0, 100) : strDescribe.toString());
            } else {
                temp.setState(EnumPracRegisterApTaxiTempState.TRANS_CHECK_NOT_PASS.getValue());
                temp.setDescribe(strDescribe.toString().length() > 100
                        ? StrUtil.sub(strDescribe.toString(), 0, 100) : strDescribe.toString());
            }
        }
    }

    /**
     * 通过pracRegisterApTaxiTemp 获取对象实例  pracApplyRegister
     *
     * @param temp 人员备案_备案信息(网约出租临时类)
     * @return pracApplyRegister
     */
    @NotNull
    private PracApplyRegister getPracApplyRegister(PracRegisterApTaxiTempDTO temp) {
        PracApplyRegister pracApplyRegister = new PracApplyRegister();
        pracApplyRegister.setPracId(temp.getPracId());
        pracApplyRegister.setPracCertId(temp.getPracCertId());
        pracApplyRegister.setPracCertNum(temp.getPracCertNum());
        pracApplyRegister.setOwnerId(temp.getOwnerId());
        pracApplyRegister.setOwnerName(temp.getOwnerName());
        pracApplyRegister.setOwnerLicenseNum(temp.getOwnerLicenseNum());
        pracApplyRegister.setOrgName(temp.getOrgName());

        pracApplyRegister.setApplyType(EnumPracApplyType.Register.getValue());
        pracApplyRegister.setCreateTime(DateUtil.date());
        //objPracRegisterApply.RegisterEndDate = DateTime.Now.AddYears(3);
        pracApplyRegister.setOtherInfo("系统导入");
        pracApplyRegister.setSource(EnumPracRegisterSource.ThisHometown.getValue());
        pracApplyRegister.setTransType(EnumVehicleTransType.ApTaxi.getValue());
        pracApplyRegister.setDeptOpinion("系统导入");
        pracApplyRegister.setVehicleNum(temp.getVehicleNum());
        pracApplyRegister.setPlateColor(temp.getPlateColor());
        pracApplyRegister.setLaborStartDate(temp.getLaborStartDate());
        pracApplyRegister.setLaborEndDate(temp.getLaborEndDate());
        pracApplyRegister.setApplySource(temp.getRegisterApplySource());
        pracApplyRegister.setApplicant(temp.getApplicant());
        return pracApplyRegister;
    }

    /**
     * 验证是否已注册 能否多平台注册
     *
     * @param temp 导入数据
     */
    private void verifyHadRegister(PracRegisterApTaxiTempDTO temp, StringBuilder strDescribe) {
        /*
            网约注册 可能是多平台注册（目前是无锡）由机构业务参数PR0043控制能否多平台注册
         */
        if (EmptyUtil.isNotEmpty(temp.getOrgId())) {
            boolean canMultiPlatform = sysBusiParamService.getBusiParamBoolValue(temp.getOrgId(), SysBusiParamService.PARAM_CODE_PR0043);
            boolean canBothRegisterRecord = sysBusiParamService.getBusiParamBoolValue(temp.getOrgId(), SysBusiParamService.PARAM_CODE_PR0050);

            // 如果可以多平台注册
            if (canMultiPlatform) {
                // 1.是否允许同时存在网约注册、巡游注册、人员备案
                LambdaQueryWrapper<PracRegister> wrapper = new LambdaQueryWrapper<>();
                if (canBothRegisterRecord) {
                    // ## 1).TRUE -> 不验证是否已有巡游注册或备案
                } else {
                    // ## 2).FALSE -> 验证是否已有巡游注册或备案
                    wrapper.eq(PracRegister::getPracCertNum, temp.getPracCertNum());
                    wrapper.ne(PracRegister::getTransType, EnumOwnerType.NetTaxi.getValue());
                    PracRegister hadOtherTransType = pracRegisterMapper.selectOne(wrapper, false);
                    if (EmptyUtil.isNotEmpty(hadOtherTransType)) {
                        String format = MessageFormat.format("此驾驶员已在[{0}]{1}，请先进行注销！", hadOtherTransType.getOwnerName(), (Objects.equals(hadOtherTransType.getTransType(), EnumVehicleTransType.Taxi.getValue()) ? "巡游注册" : "备案"));
                        strDescribe.append(format);
                    }
                }

                // 2.已在当前业户进行网约注册
                wrapper.clear();
                wrapper.eq(PracRegister::getPracCertNum, temp.getPracCertNum());
                wrapper.eq(PracRegister::getTransType, EnumOwnerType.NetTaxi.getValue());
                wrapper.eq(PracRegister::getOwnerId, temp.getOwnerId());
                PracRegister hadCurrentOwner = pracRegisterMapper.selectOne(wrapper, false);
                if (EmptyUtil.isNotEmpty(hadCurrentOwner)) {
                    String format = "此驾驶员已在当前业户注册";
                    strDescribe.append(format);
                }
            } else {
                // 1.是否允许同时存在网约注册、巡游注册、人员备案
                LambdaQueryWrapper<PracRegister> wrapper = new LambdaQueryWrapper<>();
                if (canBothRegisterRecord) {
                    // ## 1).TRUE -> 验证是否已有网约注册
                    wrapper.eq(PracRegister::getPracCertNum, temp.getPracCertNum());
                    wrapper.eq(PracRegister::getTransType, EnumOwnerType.NetTaxi.getValue());
                    PracRegister had = pracRegisterMapper.selectOne(wrapper, false);
                    if (EmptyUtil.isNotEmpty(had)) {
                        String format = MessageFormat.format("此驾驶员已在[{0}]{1}，请先进行注销！", had.getOwnerName(), ((Objects.equals(had.getTransType(), EnumVehicleTransType.Taxi.getValue()) || Objects.equals(had.getTransType(), EnumVehicleTransType.ApTaxi.getValue())) ? "注册" : "备案"));
                        strDescribe.append(format);
                    }
                } else {
                    // ## 2).FALSE -> 验证是否已有注册或备案
                    wrapper.clear();
                    wrapper.eq(PracRegister::getPracCertNum, temp.getPracCertNum());
                    PracRegister had = pracRegisterMapper.selectOne(wrapper, false);
                    if (EmptyUtil.isNotEmpty(had)) {
                        String format = MessageFormat.format("此驾驶员已在[{0}]{1}，请先进行注销！", had.getOwnerName(), ((Objects.equals(had.getTransType(), EnumVehicleTransType.Taxi.getValue()) || Objects.equals(had.getTransType(), EnumVehicleTransType.ApTaxi.getValue())) ? "注册" : "备案"));
                        strDescribe.append(format);
                    }
                }
            }
        }
    }

}
