package com.guanwei.tmis.practitioner.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanwei.core.ApplicationContextHolder;
import com.guanwei.tmis.common.entity.SysOrganize;
import com.guanwei.tmis.common.enums.*;
import com.guanwei.tmis.common.service.SysBusiParamService;
import com.guanwei.tmis.common.service.SysOrganizeService;
import com.guanwei.tmis.practitioner.common.entity.*;
import com.guanwei.tmis.practitioner.common.event.PracApplyEvent;
import com.guanwei.tmis.practitioner.common.service.*;
import com.guanwei.tmis.practitioner.service.PracApplyAutoReplaceService;
import com.guanwei.token.model.UserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;

/**
 * 网约车驾驶员自动换证服务实现
 *
 * <AUTHOR>
 * @date 2023-09-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PracApplyAutoReplaceServiceImpl implements PracApplyAutoReplaceService {

    private final PracApplyService pracApplyService;

    private final PracApplyCertService pracApplyCertService;

    private final PracApplyBaseService pracApplyBaseService;

    private final PractitionerService practitionerService;

    private final PracCertificateService pracCertificateService;

    private final SysOrganizeService sysOrganizeService;

    private final SysBusiParamService sysBusiParamService;

    private final PracAutoRecordService pracAutoRecordService;

    private final PracAutoVerifyRecordService pracAutoVerifyRecordService;

    /**
     * 提交不流程
     *
     * @param pracApply     从业人员申请
     * @param pracApplyCert 从业人员申请证书
     * @param pracApplyBase 从业人员申请基础
     * @param userInfo      用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitNotFlow(PracApply pracApply, PracApplyCert pracApplyCert, PracApplyBase pracApplyBase, UserInfo userInfo) {
        ApplicationContext applicationContext = ApplicationContextHolder.getApplicationContext();

        PracApply nowPracApply = pracApplyService.getById(pracApply.getApplyId());
        Assert.isTrue(Objects.equals(EnumApplyState.Submit.getValue(), nowPracApply.getState()), "申请已被受理，无法自动审核");

        pracApply.setState(EnumApplyState.Accepted.getValue());
        pracApply.setReasonType(EnumPracReasonType.None.getValue());
        pracApply.setDecisionResult(EnumDecisionResult.None.getValue());
        pracApply.setCheckState(EnumCheckState.None.getValue());

        //受理信息
        pracApply.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        pracApply.setAccepter(userInfo.getUserName());
        pracApply.setAccepterId(userInfo.getUserId());
        pracApply.setAcceptDate(new Date());
        pracApply.setAcceptOrgId(userInfo.getOrgId());
        pracApply.setAcceptOrgName(userInfo.getOrgName());

        //管辖信息
        pracApply.setOrgId(userInfo.getOrgId());
        pracApply.setOrgName(userInfo.getOrgName());

        //基础信息
        pracApply.setModifier(userInfo.getUserName());
        pracApply.setModifyTime(new Date());
        pracApplyService.updateById(pracApply);

        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.updateById(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        pracApplyCert.setModifyTime(new Date());
        pracApplyCertService.updateById(pracApplyCert);

        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Accepted));
    }

    /**
     * 归档不流程
     *
     * @param pracApply     从业人员申请
     * @param pracApplyCert 从业人员申请证书
     * @param pracApplyBase 从业人员申请基础
     * @param userInfo      用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void archiveNotFlow(PracApply pracApply, PracApplyCert pracApplyCert, PracApplyBase pracApplyBase, UserInfo userInfo) {
        ApplicationContext applicationContext = ApplicationContextHolder.getApplicationContext();
        Practitioner practitioner = practitionerService.getOne(pracApply.getPracId());
        Assert.notNull(practitioner, "Id为【" + pracApply.getPracId() + "】的从业人员档案信息已不存在！");

        // ************************************* 申请数据   ***************************************
        SysOrganize issueOrganize = sysOrganizeService.getIssueOrganize(pracApply.getOrgId(), EnumIndustryClass.Practitioner.getValue());
        Assert.notNull(issueOrganize, "未计算得出发证机构信息！");

        // 查询档案信息
        LambdaQueryWrapper<PracCertificate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PracCertificate::getPracId, pracApply.getPracId());
        lambdaQueryWrapper.eq(PracCertificate::getCertType, pracApplyCert.getCertType());
        PracCertificate pracCertificate = pracCertificateService.getOne(lambdaQueryWrapper);
        Assert.notNull(pracCertificate, "Id为【" + pracApply.getPracId() + "】的从业人员证件信息已不存在！");
        pracApplyCert.setPracCertStartDate(pracCertificate.getPracCertEndDate());
        Date pracCertEndDate = pracApplyService.calcPracCertEndDate(pracApply, pracApplyBase, pracApplyCert.getPracCertStartDate());
        Assert.notNull(pracCertEndDate, "未能计算得到新的有效期止时间！");
        log.info("从业人员换证，受理信息有效期止为{}", pracCertEndDate);
        pracApplyCert.setPracCertEndDate(pracCertEndDate);
        pracApplyCert.setPracCertIssueDate(new Date());
        pracApplyCert.setIssueOrgId(issueOrganize.getOrgId());
        pracApplyCert.setIssueOrgName(issueOrganize.getOrgName());
        pracApplyCertService.update(pracApplyCert);

        // ************************************* 档案数据   ***************************************
        // 1.归档从业人员档案信息
        practitioner.setPracName(pracApply.getPracName());
        practitioner.setSex(pracApplyBase.getSex());
        practitioner.setBirthday(pracApplyBase.getBirthday());
        practitioner.setNationality(pracApplyBase.getNationality());
        practitioner.setIdCertType(pracApply.getIdCertType());
        practitioner.setIdCertNum(pracApply.getIdCertNum());
        practitioner.setCounty(pracApplyBase.getCounty());
        practitioner.setPhoneNum(pracApplyBase.getPhoneNum());
        practitioner.setMobile(pracApplyBase.getMobile());
        practitioner.setIdCertAddress(pracApplyBase.getIdCertAddress());
        practitioner.setAddress(pracApplyBase.getAddress());
        practitioner.setEmail(pracApplyBase.getEmail());
        practitioner.setPostcode(pracApplyBase.getPostcode());
        practitioner.setEducation(pracApplyBase.getEducation());
        practitioner.setTechnicalPost(pracApplyBase.getTechnicalPost());
        practitioner.setHealthState(pracApplyBase.getHealthState());
        practitioner.setDrivingLicense(pracApplyBase.getDrivingLicense());
        practitioner.setDrivingVehicle(pracApplyBase.getDrivingVehicle());
        practitioner.setDrivLiceFirstDate(pracApplyBase.getDrivLiceFirstDate());
        practitioner.setDrivLiceStartDate(pracApplyBase.getDrivLiceStartDate());
        practitioner.setDrivLiceEndDate(pracApplyBase.getDrivLiceEndDate());
        practitioner.setDrivLicePeriod(pracApplyBase.getDrivLicePeriod());
        practitioner.setDrivLiceOrg(pracApplyBase.getDrivLiceOrg());
        practitioner.setDrivArchiveNum(pracApplyBase.getDrivArchiveNum());
        practitioner.setArchiveNum(pracApplyCert.getArchiveNum());
        practitioner.setPhotoPath(pracApplyBase.getPhotoPath());
        practitioner.setPracTypeNames(pracApply.getPracTypeNames());
        practitioner.setPracTypeCodes(pracApply.getPracTypeCodes());
        practitioner.setTeachVehicleFirstDate(pracApplyBase.getTeachVehicleFirstDate());
        practitioner.setSafeDriving(pracApplyBase.getSafeDriving());
        practitioner.setAreaCode(pracApply.getAreaCode());
        practitioner.setOrgId(pracApply.getOrgId());
        practitioner.setModifier(userInfo.getUserName());
        practitioner.setModifyTime(new Date());
        practitioner.setComments(pracApply.getComments());
        practitioner.setMaritalStatus(pracApplyBase.getMaritalStatus());
        practitioner.setForeignLanguageAbility(pracApplyBase.getForeignLanguageAbility());
        practitioner.setIfLocalPeople(pracApplyBase.getIfLocalPeople());
        practitioner.setCitizenship(pracApplyBase.getCitizenship());
        practitioner.setPartyMemberStatus(pracApplyBase.getPartyMemberStatus());
        practitioner.setVeteransStatus(pracApplyBase.getVeteransStatus());
        practitionerService.update(practitioner);

        //2.归档从业人员资格证信息
        pracCertificate.setCounty(pracApplyBase.getCounty());
        // pracCertificate.setArchiveNum(practitioner.getArchiveNum());
        pracCertificate.setCertNum(pracApplyCert.getCertNum());
        pracCertificate.setCertType(pracApplyCert.getCertType());
        pracCertificate.setPracCertNum(pracApplyCert.getPracCertNum());
        pracCertificate.setTeachingVehicles(pracApplyBase.getTeachingVehicles());
        pracCertificate.setPracCertStartDate(pracApplyCert.getPracCertStartDate());
        pracCertificate.setPracCertIssueDate(pracApplyCert.getPracCertIssueDate());
        pracCertificate.setPracCertEndDate(pracApplyCert.getPracCertEndDate());
        pracCertificate.setOrgId(pracApply.getOrgId());
        pracCertificate.setIssueOrgName(pracApplyCert.getIssueOrgName());
        pracCertificate.setIssueOrgId(pracApplyCert.getIssueOrgId());
        pracCertificate.setAreaCode(pracApply.getAreaCode());
        pracCertificate.setPracCertPhotoPath(pracApplyCert.getPracCertPhotoPath());
        pracCertificate.setVerifyCode(pracApplyCert.getVerifyCode());
        pracCertificate.setICCardNum(pracApplyCert.getIcCardNum());
        pracCertificate.setCardTime(pracApplyCert.getCardTime());
        pracCertificate.setPracId(practitioner.getPracId());
        pracCertificateService.update(pracCertificate);

        //重置从业人员基本信息中从业资格类型名称和代码
        resetPracTypeNameAndCode(practitioner.getPracId());

        pracApply.setDecisionResult(EnumDecisionResult.Allowed.getValue());
        pracApply.setDecisionDate(new Date());
        pracApply.setState(EnumApplyState.Approved.getValue());
        pracApply.setModifyTime(new Date());
        pracApply.setModifier(userInfo.getUserName());
        pracApplyService.update(pracApply);
        try {
            // 睡一会
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        // 韩总制证需要的消息是这条
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Approved));
        try {
            // 睡一会
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        pracApply.setCheckState(EnumCheckState.Passed.getValue());
        pracApply.setState(EnumApplyState.Completed.getValue());
        pracApply.setModifyTime(new Date());
        pracApply.setModifier(userInfo.getUserName());
        pracApplyService.update(pracApply);
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Completed));
    }

}
