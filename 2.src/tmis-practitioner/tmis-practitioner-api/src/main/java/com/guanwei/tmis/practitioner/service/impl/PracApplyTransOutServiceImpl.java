package com.guanwei.tmis.practitioner.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.core.utils.EnumUtils;
import com.guanwei.err.NoStackBizException;
import com.guanwei.tmis.common.dto.ApplyMaterial4PrintDTO;
import com.guanwei.tmis.common.dto.SPBusinessApplyRecipientDTO;
import com.guanwei.tmis.common.dto.VerifyResultDTO;
import com.guanwei.tmis.common.entity.SPBusinessApplyRecipient;
import com.guanwei.tmis.common.entity.SysDepartment;
import com.guanwei.tmis.common.entity.SysOrganize;
import com.guanwei.tmis.common.enums.*;
import com.guanwei.tmis.common.mapstruct.SPBusinessApplyRecipientMapStruct;
import com.guanwei.tmis.common.service.*;
import com.guanwei.tmis.common.utils.WorkflowUtils;
import com.guanwei.tmis.practitioner.common.dto.*;
import com.guanwei.tmis.practitioner.common.dto.mapstruct.*;
import com.guanwei.tmis.practitioner.common.entity.*;
import com.guanwei.tmis.practitioner.common.event.PracApplyEvent;
import com.guanwei.tmis.practitioner.common.service.*;
import com.guanwei.tmis.practitioner.common.service.impl.verify.PracitionerVerify;
import com.guanwei.tmis.practitioner.dto.PracTransferOutApplyPrintDTO;
import com.guanwei.tmis.practitioner.service.PracApplyTransInAddService;
import com.guanwei.tmis.practitioner.service.PracApplyTransOutService;
import com.guanwei.tmis.practitioner.service.PracRegisterApplyService;
import com.guanwei.token.model.UserInfo;
import com.guanwei.workflow.config.WorkflowProperties;
import com.guanwei.workflow.dto.WorkflowProcessDataDTO;
import com.guanwei.workflow.model.WorkflowVar;
import com.guanwei.workflow.service.WorkflowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.text.MessageFormat;
import java.util.*;

import static com.guanwei.workflow.utils.NetCapUtils.getMessage;

/**
 * 从业人员转出申请实现类
 *
 * <AUTHOR>
 * @date 2022/09/16 11:39:46
 */
@Slf4j
@SuppressWarnings("unused")
@Service
@RequiredArgsConstructor
public class PracApplyTransOutServiceImpl implements PracApplyTransOutService {

    private final WorkflowService workflowService;

    private final PracApplyMapStruct pracApplyMapStruct;

    private final PracApplyBaseMapStruct pracApplyBaseMapStruct;

    private final PracApplyTransferOutMapStruct pracApplyTransferOutMapStruct;

    private final PracApplyCertMapStruct pracApplyCertMapStruct;

    private final PracApplyMaterialMapStruct pracApplyMaterialMapStruct;

    private final PracApplyExamMapStruct pracApplyExamMapStruct;

    private final PracApplyExamResultMapStruct pracApplyExamResultMapStruct;

    private final SPBusinessApplyRecipientMapStruct spBusinessApplyRecipientMapStruct;

    private final PractitionerService practitionerService;

    private final PracAttachmentMapStruct pracAttachmentMapStruct;

    private final PracApplyService pracApplyService;

    private final PracApplyBaseService pracApplyBaseService;

    private final PracApplyTransferOutService pracApplyTransferOutService;

    private final PracApplyCertService pracApplyCertService;

    private final PracApplyExamService pracApplyExamService;

    private final PracApplyExamResultService pracApplyExamResultService;

    private final PracApplyMaterialService pracApplyMaterialService;

    private final PracAttachmentService pracAttachmentService;

    private final SpArchiveNumRuleService spArchiveNumRuleService;

    private final PracCertificateService pracCertificateService;

    private final SysOrganizeService sysOrganizeService;

    private final SPBusinessApplyRecipientService spBusinessApplyRecipientService;

    private final PracApplyCommonInfoDTOMapStruct pracApplyBaseInfoOutMapStruct;

    private final PracEducationService pracEducationService;

    private final PracQualificationService pracQualificationService;

    private final PracApplyTransInAddService pracApplyTransInAddService;

    private final SysBusiParamService sysBusiParamService;

    private final PracRegisterApplyService pracRegisterApplyService;

    private final ComBsVerifyService comBsVerifyService;

    private final SysDepartmentService sysDepartmentService;

    private final ApplicationContext applicationContext;

    private final CodeRuleService codeRuleService;


    private final RabbitTemplate rabbitTemplate;

    private final WorkflowProperties workflowProperties;

    private final PracitionerVerify pracitionerVerify;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submit(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        log.info("{},提交的信息:PracApplyCommonInfoDTO={},UserInfo={}", businessName(), pracApplyCommonInfoDTO, userInfo);
        //1.归档从业资格申请表
        //先拼接数据
        pracApplyCommonInfoDTO.setSource(EnumApplySource.WindowAccept.getValue());

        //管辖机构信息从证件档案表读取,人员档案表中的管辖机构用于历史多头管理需求，以证件表管辖机构为准
        PracCertificate pracCertificate = pracCertificateService.getValidPracCertificateByIdCertNum(pracApplyCommonInfoDTO.getIdCertNum(),
                EnumPracCertType.Normal.getValue(), pracApplyCommonInfoDTO.getAreaCode());
        Assert.notNull(pracCertificate, "从业人员证件信息为空！请联系管理员处理数据");
        pracApplyCommonInfoDTO.setOrgId(pracCertificate.getOrgId());
        SysOrganize sysOrganize = sysOrganizeService.getById(pracCertificate.getOrgId());
        Assert.notNull(sysOrganize, "从业人员管辖机构为空！请联系管理员处理数据");

        pracApplyCommonInfoDTO.setDepId(userInfo.getDepId());
        pracApplyCommonInfoDTO.setDepName(userInfo.getDepName());
        pracApplyCommonInfoDTO.setOrgName(sysOrganize.getOrgName());
        pracApplyCommonInfoDTO.setAreaCode(sysOrganize.getAreaCode());
        pracApplyCommonInfoDTO.setCreator(userInfo.getUserName());
        pracApplyCommonInfoDTO.setModifier(userInfo.getUserName());
        pracApplyCommonInfoDTO.setCreateTime(new Date());
        pracApplyCommonInfoDTO.setModifyTime(new Date());

        pracApplyCommonInfoDTO.setCheckState(EnumWebsiteTwoCheckState.None.getValue());
        pracApplyCommonInfoDTO.setReasonType(EnumPracReasonType.None.getValue());
        pracApplyCommonInfoDTO.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        pracApplyCommonInfoDTO.setAcceptDate(new Date());
        pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        pracApplyCommonInfoDTO.setState(EnumApplyState.Accepted.getValue());
        pracApplyCommonInfoDTO.setDecisionResult(EnumDecisionResult.None.getValue());


        pracApplyCommonInfoDTO.setOperator(userInfo.getUserName());

        //业务验证
        comBsVerifyService.bsCommonVerify(pracApplyCommonInfoDTO,
                EnumIndustryClass.Practitioner.getValue(), "0",
                pracApplyCommonInfoDTO.getApplyType(), pracApplyCommonInfoDTO.getOrgId());
        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        PracApply pracApply = pracApplyMapStruct.toSource(pracApplyDTO);
        pracApplyService.save(pracApply);

        //归档转出申请表
        PracApplyTransferOutDTO pracApplyTransferOutDTO = pracApplyCommonInfoDTO.getPracApplyTransferOut();
        PracApplyTransferOut pracApplyTransferOut = pracApplyTransferOutMapStruct.toSource(pracApplyTransferOutDTO);
        pracApplyTransferOut.setTransferOutOrgName(sysOrganize.getOrgName());
        pracApplyTransferOut.setApplyId(pracApply.getApplyId());
        pracApplyTransferOutService.save(pracApplyTransferOut);

        //2.归档从业资格申请基础表
        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        PracApplyBase pracApplyBase = pracApplyBaseMapStruct.toSource(pracApplyBaseDTO);
        pracApplyBase.setApplyId(pracApply.getApplyId());
        pracApplyBase.setCreateTime(new Date());
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.save(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.归档从业资格申请基础表
        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();
        PracApplyCert pracApplyCert = pracApplyCertMapStruct.toSource(pracApplyCertDTO);
        pracApplyCert.setApplyId(pracApply.getApplyId());
        pracApplyCertService.save(pracApplyCert);

        //4.材料信息，先把之前提交的材料删除，以新提交的材料清单重新入库
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        pracApplyMaterialService.savePracApplyMaterial(pracApplyMaterials, pracApply.getApplyId());

        // 5.归档申请人邮寄信息
        if (Objects.equals(pracApplyDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.save(spBusinessApplyRecipient);
        }

        // 案号生成
        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //7.工作流
        StringBuilder bizObjectName = new StringBuilder(pracApplyDTO.getPracName());
        if (EmptyUtil.isNotEmpty(pracApply.getIdCertNum())) {
            bizObjectName.append(MessageFormat.format("（身份证：{0}）", pracApplyDTO.getIdCertNum()));
        }
        if (EmptyUtil.isNotEmpty(pracApplyCert.getArchiveNum())) {
            bizObjectName.append(MessageFormat.format("（档案号：{0}）", pracApplyCert.getArchiveNum()));
        }
        workflowService.startWorkflow(WorkflowUtils.convertToWorkflowUser(userInfo), pracApply.getApplyId(), pracApply.getBusinessNo(), bizObjectName, pracApplyCommonInfoDTO.getProcessData());
        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //8.发布事件
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Created));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void accept(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        log.info("{},提交的信息:PracApplyCommonInfoDTO={},UserInfo={}", businessName(), pracApplyCommonInfoDTO, userInfo);
        //企业端登记时没有这些信息
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAccepter())) {
            pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAccepterId())) {
            pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptOrgId())) {
            pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptOrgName())) {
            pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptDate())) {
            pracApplyCommonInfoDTO.setAcceptDate(new Date());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getOperator())) {
            pracApplyCommonInfoDTO.setOperator(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getTelephone())) {
            pracApplyCommonInfoDTO.setTelephone(userInfo.getMobile());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getMobile())) {
            pracApplyCommonInfoDTO.setMobile(userInfo.getMobile());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getDepId())) {
            pracApplyCommonInfoDTO.setDepId(userInfo.getDepId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getDepName())) {
            pracApplyCommonInfoDTO.setDepName(userInfo.getDepName());
        }
        pracApplyCommonInfoDTO.setDecisionResult(EnumDecisionResult.None.getValue());
        pracApplyCommonInfoDTO.setReasonType(EnumPracReasonType.None.getValue());
        pracApplyCommonInfoDTO.setCheckState(EnumCheckState.None.getValue());
        pracApplyCommonInfoDTO.setState(EnumApplyState.Accepted.getValue());
        pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        pracApplyCommonInfoDTO.setAcceptDate(new Date());

        pracApplyCommonInfoDTO.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        PracApplyTransferOutDTO pracApplyTransferOutDTO = pracApplyCommonInfoDTO.getPracApplyTransferOut();
        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();
        //业务验证
        comBsVerifyService.bsCommonVerify(pracApplyCommonInfoDTO,
                EnumIndustryClass.Practitioner.getValue(), "0",
                pracApplyCommonInfoDTO.getApplyType(), pracApplyCommonInfoDTO.getOrgId());
        //1.更新申请表
        String applyId = pracApplyDTO.getApplyId();
        PracApply pracApply = pracApplyService.getById(applyId);
        Assert.notNull(pracApply, "Id为【" + applyId + "】的从业人员申请信息已不存在！");

        pracApplyMapStruct.updateToSource(pracApplyDTO, pracApply);
        pracApply.setModifier(userInfo.getUserName());
        pracApply.setModifyTime(new Date());
        pracApplyService.updateById(pracApply);

        //归档转出申请表
        PracApplyTransferOut pracApplyTransferOut = pracApplyTransferOutService.getById(applyId);
        Assert.notNull(pracApplyTransferOut, "Id为【" + applyId + "】的从业人员转出申请信息不存在！");
        pracApplyTransferOutMapStruct.updateToSource(pracApplyTransferOutDTO, pracApplyTransferOut);
        pracApplyTransferOutService.updateById(pracApplyTransferOut);

        //2.申请基础信息
        PracApplyBase pracApplyBase = pracApplyBaseService.getById(applyId);
        Assert.notNull(pracApplyBase, "Id为【" + applyId + "】的从业人员申请基础信息不存在！");
        pracApplyBaseMapStruct.updateToSource(pracApplyBaseDTO, pracApplyBase);
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.updateById(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.证件信息
        PracApplyCert pracApplyCert = pracApplyCertService.getById(applyId);
        Assert.notNull(pracApplyCert, "Id为【" + applyId + "】的从业人员申请证件信息不存在！");
        pracApplyCertMapStruct.updateToSource(pracApplyCertDTO, pracApplyCert);
        pracApplyCertService.updateById(pracApplyCert);

        //4.材料信息，先把之前提交的材料删除，以新提交的材料清单重新入库
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        pracApplyMaterialService.savePracApplyMaterial(pracApplyMaterials, pracApply.getApplyId());

        //5.邮寄信息，先把之前提交的删除，以新提交的数据重新入库
        //先删除
        spBusinessApplyRecipientService.remove(Wrappers.lambdaQuery(SPBusinessApplyRecipient.class).eq(SPBusinessApplyRecipient::getApplyId, applyId));
        if (Objects.equals(pracApplyDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            //归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.save(spBusinessApplyRecipient);
        }


        List<WorkflowVar> workflowVars = Lists.newArrayList();
        WorkflowProcessDataDTO workflowProcessDataDTO = pracApplyCommonInfoDTO.getProcessData();
        workflowService.submitWorkflow(workflowProcessDataDTO, workflowVars, WorkflowUtils.convertToWorkflowUser(userInfo));

        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //9.发布事件
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Accepted));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void wxAccept(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        log.info("{},网上申请受理提交的信息:PracApplyCommonInfoDTO={},UserInfo={}", businessName(), pracApplyCommonInfoDTO, userInfo);
        //企业端登记时没有这些信息
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAccepter())) {
            pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAccepterId())) {
            pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptOrgId())) {
            pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptOrgName())) {
            pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptDate())) {
            pracApplyCommonInfoDTO.setAcceptDate(new Date());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getOperator())) {
            pracApplyCommonInfoDTO.setOperator(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getTelephone())) {
            pracApplyCommonInfoDTO.setTelephone(userInfo.getMobile());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getMobile())) {
            pracApplyCommonInfoDTO.setMobile(userInfo.getMobile());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getDepId())) {
            pracApplyCommonInfoDTO.setDepId(userInfo.getDepId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getDepName())) {
            pracApplyCommonInfoDTO.setDepName(userInfo.getDepName());
        }
        pracApplyCommonInfoDTO.setDecisionResult(EnumDecisionResult.None.getValue());
        pracApplyCommonInfoDTO.setReasonType(EnumPracReasonType.None.getValue());
        pracApplyCommonInfoDTO.setCheckState(EnumCheckState.None.getValue());
        pracApplyCommonInfoDTO.setState(EnumApplyState.Accepted.getValue());
        pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        pracApplyCommonInfoDTO.setAcceptDate(new Date());

        pracApplyCommonInfoDTO.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        PracApplyTransferOutDTO pracApplyTransferOutDTO = pracApplyCommonInfoDTO.getPracApplyTransferOut();
        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();
        //业务验证
        comBsVerifyService.bsCommonVerify(pracApplyCommonInfoDTO,
                EnumIndustryClass.Practitioner.getValue(), "0",
                pracApplyCommonInfoDTO.getApplyType(), pracApplyCommonInfoDTO.getOrgId());
        //1.更新申请表
        String applyId = pracApplyDTO.getApplyId();
        PracApply pracApply = pracApplyService.getById(applyId);
        Assert.notNull(pracApply, "Id为【" + applyId + "】的从业人员申请信息已不存在！");

        pracApplyMapStruct.updateToSource(pracApplyDTO, pracApply);
        pracApply.setModifier(userInfo.getUserName());
        pracApply.setModifyTime(new Date());
        pracApplyService.updateById(pracApply);

        //归档转出申请表
        PracApplyTransferOut pracApplyTransferOut = pracApplyTransferOutService.getById(applyId);
        Assert.notNull(pracApplyTransferOut, "Id为【" + applyId + "】的从业人员转出申请信息不存在！");
        pracApplyTransferOutMapStruct.updateToSource(pracApplyTransferOutDTO, pracApplyTransferOut);
        pracApplyTransferOutService.updateById(pracApplyTransferOut);

        //2.申请基础信息
        PracApplyBase pracApplyBase = pracApplyBaseService.getById(applyId);
        Assert.notNull(pracApplyBase, "Id为【" + applyId + "】的从业人员申请基础信息不存在！");
        pracApplyBaseMapStruct.updateToSource(pracApplyBaseDTO, pracApplyBase);
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.updateById(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.证件信息
        PracApplyCert pracApplyCert = pracApplyCertService.getById(applyId);
        Assert.notNull(pracApplyCert, "Id为【" + applyId + "】的从业人员申请证件信息不存在！");
        pracApplyCertMapStruct.updateToSource(pracApplyCertDTO, pracApplyCert);
        pracApplyCertService.updateById(pracApplyCert);

        //4.材料信息，先把之前提交的材料删除，以新提交的材料清单重新入库
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        pracApplyMaterialService.savePracApplyMaterial(pracApplyMaterials, pracApply.getApplyId());

        //5.邮寄信息，先把之前提交的删除，以新提交的数据重新入库
        //先删除
        spBusinessApplyRecipientService.remove(Wrappers.lambdaQuery(SPBusinessApplyRecipient.class).eq(SPBusinessApplyRecipient::getApplyId, applyId));
        if (Objects.equals(pracApplyDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            //归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.save(spBusinessApplyRecipient);
        }

        pracApplyCommonInfoDTO.getProcessData().setProcessId("00107");
        StringBuilder bizObjectName = new StringBuilder(pracApplyDTO.getPracName());
        if (EmptyUtil.isNotEmpty(pracApply.getIdCertNum())) {
            bizObjectName.append(MessageFormat.format("（身份证：{0}）", pracApplyDTO.getIdCertNum()));
        }
        if (EmptyUtil.isNotEmpty(pracApplyCert.getArchiveNum())) {
            bizObjectName.append(MessageFormat.format("（档案号：{0}）", pracApplyCert.getArchiveNum()));
        }
        workflowService.startWorkflow(WorkflowUtils.convertToWorkflowUser(userInfo), pracApply.getApplyId(), pracApply.getBusinessNo(), bizObjectName, pracApplyCommonInfoDTO.getProcessData());

        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //9.发布事件
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Accepted));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void acceptAndCheck(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        log.info("{},受理审核提交的信息:PracApplyCommonInfoDTO={},UserInfo={}", businessName(), pracApplyCommonInfoDTO, userInfo);
        String applyId = pracApplyCommonInfoDTO.getApplyId();
        PracApply pracApply = pracApplyService.getById(applyId);
        Optional<EnumApplyState> enumApplyState = EnumApplyState.getByState(pracApply.getState());
        if (Objects.equals(pracApply.getState(), EnumApplyState.Checked.getValue()) || Objects.equals(pracApply.getState(), EnumApplyState.Approved.getValue()) || Objects.equals(pracApply.getState(), EnumApplyState.Completed.getValue())) {
            if (enumApplyState.isPresent()) {
                throw new NoStackBizException("申请信息【ApplyId=" + pracApplyCommonInfoDTO.getApplyId()
                        + "】" + enumApplyState.get().getDescription() + ",请勿重复审核！");
            }
        }
        if (Objects.equals(pracApply.getState(), EnumApplyState.HangUp.getValue())) {
            throw new NoStackBizException("申请信息[ " + pracApply.getPracName() + "的" + EnumPracApplyType.getByPracApplyType(pracApply.getApplyType()).get().getDescription() + "]业务流程已被挂起，需等上游业务归档后才能解挂，预计解挂时间视上游业务进度而定。感谢您的耐心等待！");
        }
        //企业端登记时没有这些信息
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAccepter())) {
            pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAccepterId())) {
            pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptOrgId())) {
            pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptOrgName())) {
            pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getAcceptDate())) {
            pracApplyCommonInfoDTO.setAcceptDate(new Date());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getOperator())) {
            pracApplyCommonInfoDTO.setOperator(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getTelephone())) {
            pracApplyCommonInfoDTO.setTelephone(userInfo.getMobile());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getMobile())) {
            pracApplyCommonInfoDTO.setMobile(userInfo.getMobile());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getDepId())) {
            pracApplyCommonInfoDTO.setDepId(userInfo.getDepId());
        }
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getDepName())) {
            pracApplyCommonInfoDTO.setDepName(userInfo.getDepName());
        }
        pracApplyCommonInfoDTO.setDecisionResult(EnumDecisionResult.None.getValue());
        pracApplyCommonInfoDTO.setReasonType(EnumPracReasonType.None.getValue());
        pracApplyCommonInfoDTO.setCheckState(EnumCheckState.None.getValue());
        pracApplyCommonInfoDTO.setState(EnumApplyState.Accepted.getValue());
        pracApplyCommonInfoDTO.setAccepter(userInfo.getUserName());
        pracApplyCommonInfoDTO.setAccepterId(userInfo.getUserId());
        pracApplyCommonInfoDTO.setAcceptOrgId(userInfo.getOrgId());
        pracApplyCommonInfoDTO.setAcceptOrgName(userInfo.getOrgName());
        pracApplyCommonInfoDTO.setAcceptDate(new Date());

        pracApplyCommonInfoDTO.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        PracApplyTransferOutDTO pracApplyTransferOutDTO = pracApplyCommonInfoDTO.getPracApplyTransferOut();
        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();

        //1.更新申请表

        Assert.notNull(pracApply, "Id为【" + applyId + "】的从业人员申请信息已不存在！");
        //业务验证
        comBsVerifyService.bsCommonVerify(pracApplyCommonInfoDTO,
                EnumIndustryClass.Practitioner.getValue(), EnumPracType.PracTypeCodeNetDriver.getValue(),
                pracApplyCommonInfoDTO.getApplyType(), pracApplyCommonInfoDTO.getOrgId());
        pracApplyMapStruct.updateToSource(pracApplyDTO, pracApply);
        pracApply.setModifier(userInfo.getUserName());
        pracApply.setModifyTime(new Date());
        pracApplyService.updateById(pracApply);

        //归档转出申请表
        PracApplyTransferOut pracApplyTransferOut = pracApplyTransferOutService.getById(applyId);
        Assert.notNull(pracApplyTransferOut, "Id为【" + applyId + "】的从业人员转出申请信息不存在！");
        pracApplyTransferOutMapStruct.updateToSource(pracApplyTransferOutDTO, pracApplyTransferOut);
        pracApplyTransferOutService.updateById(pracApplyTransferOut);

        //2.申请基础信息
        PracApplyBase pracApplyBase = pracApplyBaseService.getById(applyId);
        Assert.notNull(pracApplyBase, "Id为【" + applyId + "】的从业人员申请基础信息不存在！");
        pracApplyBaseMapStruct.updateToSource(pracApplyBaseDTO, pracApplyBase);
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.updateById(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.证件信息
        PracApplyCert pracApplyCert = pracApplyCertService.getById(applyId);
        Assert.notNull(pracApplyCert, "Id为【" + applyId + "】的从业人员申请证件信息不存在！");
        pracApplyCertMapStruct.updateToSource(pracApplyCertDTO, pracApplyCert);
        pracApplyCertService.updateById(pracApplyCert);

        //4.材料信息，先把之前提交的材料删除，以新提交的材料清单重新入库
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        pracApplyMaterialService.savePracApplyMaterial(pracApplyMaterials, pracApply.getApplyId());

        //5.邮寄信息，先把之前提交的删除，以新提交的数据重新入库
        //先删除
        spBusinessApplyRecipientService.remove(Wrappers.lambdaQuery(SPBusinessApplyRecipient.class).eq(SPBusinessApplyRecipient::getApplyId, applyId));
        if (Objects.equals(pracApplyDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            //归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.save(spBusinessApplyRecipient);
        }

        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        List<WorkflowVar> workflowVars = Lists.newArrayList();
        WorkflowProcessDataDTO workflowProcessDataDTO = pracApplyCommonInfoDTO.getProcessData();
        workflowService.submitWorkflow(workflowProcessDataDTO, workflowVars, WorkflowUtils.convertToWorkflowUser(userInfo));

        //9.发布受理事件
        CorrelationData correlationDataAccept = new CorrelationData(pracApply.getApplyId() + UUID.randomUUID());
        Message messageAccept = getMessage(pracApply, EnumPracApplyEvent.Accepted.getValue(), "PracApplyDto");
        rabbitTemplate.convertAndSend(workflowProperties.getRabbitExchange(), EnumPracApplyEvent.Accepted.getValue(), messageAccept, correlationDataAccept);

        //10.发布审核事件
        LambdaUpdateWrapper<PracApply> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        //便民运政来的转出申请，需要挂起
        Integer applyState = pracApply.getSource().equals(EnumApplySource.ConvenientPeople.getValue())
                && pracApply.getApplyType().equals(EnumPracApplyType.TransferOut.getValue()) ? EnumApplyState.HangUp.getValue() : EnumApplyState.Checked.getValue();
        lambdaUpdateWrapper.set(PracApply::getState, applyState);
        lambdaUpdateWrapper.set(PracApply::getChecker, userInfo.getUserName());
        lambdaUpdateWrapper.set(PracApply::getCheckTime, new Date());
        lambdaUpdateWrapper.set(PracApply::getCheckState, EnumCheckState.Passed.getValue());

        lambdaUpdateWrapper.set(PracApply::getModifier, userInfo.getUserName());
        lambdaUpdateWrapper.set(PracApply::getModifyTime, new Date());
        lambdaUpdateWrapper.eq(PracApply::getApplyId, applyId);
        pracApplyService.update(lambdaUpdateWrapper);

        CorrelationData correlationDataCheck = new CorrelationData(pracApply.getApplyId() + UUID.randomUUID());
        Message messageCheck = getMessage(pracApply, EnumPracApplyEvent.Checked.getValue(), "PracApplyDto");
        rabbitTemplate.convertAndSend(workflowProperties.getRabbitExchange(), EnumPracApplyEvent.Checked.getValue(), messageCheck, correlationDataCheck);
    }

    /**
     * 转出申请归档
     *
     * @param pracApply     进行申请
     * @param pracApplyBase 从业资格申请基础信息
     * @param pracApplyCert 从业资格申请证件信息
     * @param userInfo      用户信息
     */
    @SuppressWarnings({"AlibabaMethodTooLong", "AlibabaRemoveCommentedCode", "unused"})
    @Override
    public void archive(PracApply pracApply, PracApplyBase pracApplyBase, PracApplyCert pracApplyCert, UserInfo userInfo) {
        //************************************* 档案数据   ***************************************

        //1.归档从业人员档案信息
        Practitioner practitioner = practitionerService.getById(pracApply.getPracId());
        Assert.notNull(practitioner, "从业人员档案信息不可为空！");

        //2.归档从业人员资格证信息
        PracCertificate pracCertificate = pracCertificateService.getValidPracCertificateByIdCertNum(pracApply.getIdCertNum(), pracApplyCert.getCertType(), pracApply.getAreaCode().substring(0, 4));
        Assert.notNull(pracCertificate, "从业人员资格证信息不存在！");
        pracCertificate.setCertState(EnumCertificateState.Invalid.getValue());
        pracCertificate.setPracState(EnumPractitionerState.TransferOut.getValue());
        pracCertificateService.update(pracCertificate);

        // 清除从业人员的所有从业资格(保留教练员的资格)
        //2.删除从业资格
        pracQualificationService.deletePracQualificationByPracId(practitioner.getPracId(), pracCertificate.getPracCertId());

        Assert.notNull(practitioner.getPracTypeCodes(), "档案表中从业资格代码为空!");
        Assert.notNull(practitioner.getPracTypeNames(), "档案表中从业资格名称为空!");

        //从档案中移除转出的从业资格代码和名称
        String remainingCodes = String.join(",", CollectionUtil.subtractToList(Arrays.asList(practitioner.getPracTypeCodes().split(",")), Arrays.asList(pracCertificate.getPracTypeCodes().split(","))));
        String remainingNames = String.join(",", CollectionUtil.subtractToList(Arrays.asList(practitioner.getPracTypeNames().split(",")), Arrays.asList(pracCertificate.getPracTypeNames().split(","))));
        practitioner.setPracTypeCodes(remainingCodes);
        practitioner.setPracTypeNames(remainingNames);

        practitioner.setDrivingLicense(pracApplyBase.getDrivingLicense());
        practitioner.setDrivingVehicle(pracApplyBase.getDrivingVehicle());
        practitioner.setDrivLiceFirstDate(pracApplyBase.getDrivLiceFirstDate());
        practitioner.setDrivLiceStartDate(pracApplyBase.getDrivLiceStartDate());
        practitioner.setDrivLiceEndDate(pracApplyBase.getDrivLiceEndDate());
        practitioner.setDrivLicePeriod(pracApplyBase.getDrivLicePeriod());
        practitioner.setDrivLiceOrg(pracApplyBase.getDrivLiceOrg());
        practitioner.setPartyMemberStatus(pracApplyBase.getPartyMemberStatus());
        practitioner.setVeteransStatus(pracApplyBase.getVeteransStatus());

        //是否在岗
        practitioner.setOnJob(EnumPracIsOnJob.NotOnJob.getValue());
        practitioner.setNotJobDate(new Date());
        practitionerService.update(practitioner);

        //申请状态更新为已批准(待制证)
        LambdaUpdateWrapper<PracApply> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(PracApply::getState, EnumApplyState.Approved.getValue());
        lambdaUpdateWrapper.set(PracApply::getPracId, practitioner.getPracId());
        lambdaUpdateWrapper.set(PracApply::getModifier, userInfo.getUserName());
        lambdaUpdateWrapper.set(PracApply::getModifyTime, new Date());
        lambdaUpdateWrapper.eq(PracApply::getApplyId, pracApply.getApplyId());
        pracApplyService.update(lambdaUpdateWrapper);
        //未完成的继续教育设置为无效
        pracEducationService.setPracEducationStateInvalid(practitioner.getPracId());

        //修改备案表信息 撤销备案
        pracRegisterApplyService.addPracRegisterCancelApplyToArchived(practitioner.getPracId(), "转出时自动撤销备案", pracApplyCert.getCertType(),
                null, userInfo, pracApply);

        //重置从业人员基本信息中从业资格类型名称和代码
        resetPracTypeNameAndCode(practitioner.getPracId());
    }

    @Override
    public PracTransferOutApplyPrintDTO print(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        PracTransferOutApplyPrintDTO pracTransferOutApplyPrintDTO = new PracTransferOutApplyPrintDTO();
        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();
        PracApplyTransferOutDTO pracApplyTransferOutDTO = pracApplyCommonInfoDTO.getPracApplyTransferOut();

        pracTransferOutApplyPrintDTO.setPracName(pracApplyCommonInfoDTO.getPracName());
        pracTransferOutApplyPrintDTO.setSexName(EnumUtils.getEnumDescriotionByValue(pracApplyBaseDTO.getSex(),
                EnumSex.class).toString());
        pracTransferOutApplyPrintDTO.setIdCertNum(pracApplyCommonInfoDTO.getIdCertNum());
        pracTransferOutApplyPrintDTO.setMobile(pracApplyCommonInfoDTO.getMobile());
        pracTransferOutApplyPrintDTO.setPracCertNum(pracApplyCertDTO.getPracCertNum());
        pracTransferOutApplyPrintDTO.setPracTypeNames(pracApplyCommonInfoDTO.getPracTypeNames());
        pracTransferOutApplyPrintDTO.setPracTypeCodes(pracApplyCommonInfoDTO.getPracTypeCodes());
        pracTransferOutApplyPrintDTO.setPracCertFirstDate(pracApplyCertDTO.getPracCertFirstDate());
        pracTransferOutApplyPrintDTO.setOldOwnerName(pracApplyTransferOutDTO.getOldOwnerName());
        pracTransferOutApplyPrintDTO.setOldOwnerProvince(pracApplyTransferOutDTO.getOldOwnerProvince());
        pracTransferOutApplyPrintDTO.setOldOwnerCounty(pracApplyTransferOutDTO.getOldOwnerCounty());
        pracTransferOutApplyPrintDTO.setOldOwnerCity(pracApplyTransferOutDTO.getOldOwnerCity());
        //转出地址就是原从业单位地址
        pracTransferOutApplyPrintDTO.setOldOwnerAddress(pracApplyTransferOutDTO.getTransferOutAddress());
        pracTransferOutApplyPrintDTO.setTransferInOwnerName(pracApplyTransferOutDTO.getTransferInOwnerName());
        pracTransferOutApplyPrintDTO.setTransferInOwnerProvince(pracApplyTransferOutDTO.getTransferInOwnerProvince());
        pracTransferOutApplyPrintDTO.setTransferInOwnerCity(pracApplyTransferOutDTO.getTransferInOwnerCity());
        pracTransferOutApplyPrintDTO.setTransferInOwnerCounty(pracApplyTransferOutDTO.getTransferInOwnerCounty());
        pracTransferOutApplyPrintDTO.setApplyReason(pracApplyCommonInfoDTO.getApplyReason());
        pracTransferOutApplyPrintDTO.setComments(pracApplyCommonInfoDTO.getComments());
        //20230606，与项目经理确认后，南京特殊处理，一支队受理的打印出租车参数，二支队受理的打印从业人员参数
        if ("320100".equals(userInfo.getAreaCode())) {
            SysDepartment sysDepartment = sysDepartmentService.getParentDep(userInfo.getDepId());
            Assert.notNull(sysDepartment, "当前登录人所属部门不可为空");
            if (EmptyUtil.isNotEmpty(sysDepartment.getDepName()) && sysDepartment.getDepName().contains("一支队")) {
                pracTransferOutApplyPrintDTO.setTransferOutContact(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), SysBusiParamService.PRAC_NET_TRANSOUT_ORG_CONTACT));
                pracTransferOutApplyPrintDTO.setTransferOutFax(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), SysBusiParamService.PRAC_NET_TRANSOUT_ORG_FAX));
                pracTransferOutApplyPrintDTO.setTransferOutAddress(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), SysBusiParamService.PRAC_NET_TRANSOUT_ORG_ADDRESS));
                pracTransferOutApplyPrintDTO.setTransferOutTelephone(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), SysBusiParamService.PRAC_NET_TRANSOUT_ORG_PHONE));
            } else {
                pracTransferOutApplyPrintDTO.setTransferOutContact(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), SysBusiParamService.PRAC_TRANSOUT_ORG_CONTACT));
                pracTransferOutApplyPrintDTO.setTransferOutFax(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), SysBusiParamService.PRAC_TRANSOUT_ORG_FAX));
                pracTransferOutApplyPrintDTO.setTransferOutAddress(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), SysBusiParamService.PRAC_TRANSOUT_ORG_ADDRESS));
                pracTransferOutApplyPrintDTO.setTransferOutTelephone(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), SysBusiParamService.PRAC_TRANSOUT_ORG_PHONE));
            }
        } else {
            pracTransferOutApplyPrintDTO.setTransferOutContact(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), pracApplyCertDTO.getCertType() == EnumPracCertType.Normal.getValue() ?
                    SysBusiParamService.PRAC_TRANSOUT_ORG_CONTACT :
                    SysBusiParamService.PRAC_NET_TRANSOUT_ORG_CONTACT));
            pracTransferOutApplyPrintDTO.setTransferOutFax(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), pracApplyCertDTO.getCertType() == EnumPracCertType.Normal.getValue() ?
                    SysBusiParamService.PRAC_TRANSOUT_ORG_FAX :
                    SysBusiParamService.PRAC_NET_TRANSOUT_ORG_FAX));
            //转出地通讯地址根据机构业务参数获取
            pracTransferOutApplyPrintDTO.setTransferOutAddress(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), pracApplyCertDTO.getCertType() == EnumPracCertType.Normal.getValue() ?
                    SysBusiParamService.PRAC_TRANSOUT_ORG_ADDRESS :
                    SysBusiParamService.PRAC_NET_TRANSOUT_ORG_ADDRESS));
            pracTransferOutApplyPrintDTO.setTransferOutTelephone(sysBusiParamService.getBusiParamStringValue(pracApplyCommonInfoDTO.getOrgId(), pracApplyCertDTO.getCertType() == EnumPracCertType.Normal.getValue() ?
                    SysBusiParamService.PRAC_TRANSOUT_ORG_PHONE :
                    SysBusiParamService.PRAC_NET_TRANSOUT_ORG_PHONE));
        }
        pracTransferOutApplyPrintDTO.setTransferOutOrgName(pracApplyTransferOutDTO.getTransferOutOrgName());
        pracTransferOutApplyPrintDTO.setTransferInOrgName(pracApplyTransferOutDTO.getTransferInOrgName());
        pracTransferOutApplyPrintDTO.setTransferInAddress(pracApplyTransferOutDTO.getTransferInAddress());
        pracTransferOutApplyPrintDTO.setTransferInContact(pracApplyTransferOutDTO.getTransferInContact());
        pracTransferOutApplyPrintDTO.setTransferInTelephone(pracApplyTransferOutDTO.getTransferInTelephone());
        pracTransferOutApplyPrintDTO.setTransferInFax(pracApplyTransferOutDTO.getTransferInFax());
        List<ApplyMaterial4PrintDTO> applyMaterial4Prints = new ArrayList();
        List<PracApplyMaterialExDTO> materials = pracApplyCommonInfoDTO.getMaterials();
        if (EmptyUtil.isNotEmpty(materials)) {
            materials.forEach(material -> {
                ApplyMaterial4PrintDTO applyMaterial4PrintDTO = new ApplyMaterial4PrintDTO();
                applyMaterial4PrintDTO.setApplyMaterialId(material.getApplyMaterialId());
                applyMaterial4PrintDTO.setApplyId(material.getApplyId());
                applyMaterial4PrintDTO.setMaterialName(material.getMaterialName());
                applyMaterial4PrintDTO.setHasProvided(material.getAttachs().size() > 0);
                applyMaterial4Prints.add(applyMaterial4PrintDTO);
            });
        }
        pracTransferOutApplyPrintDTO.setApplyMaterial4Prints(applyMaterial4Prints);
        return pracTransferOutApplyPrintDTO;
    }

    /**
     * 普货一件事转籍转出登记提交
     *
     * @param pracApplyCommonInfoDTO 从业人员申请通用信息
     * @param userInfo               用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generalGoodsSubmit(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        log.info("{},普货一件事转籍提交的信息:PracApplyCommonInfoDTO={},UserInfo={}", businessName(), pracApplyCommonInfoDTO, userInfo);
        //1.归档从业资格申请表
        //先拼接数据
        pracApplyCommonInfoDTO.setSource(EnumApplySource.OneThing.getValue());
        pracApplyCommonInfoDTO.setReasonType(EnumPracReasonType.None.getValue());
        pracApplyCommonInfoDTO.setCheckState(EnumWebsiteTwoCheckState.None.getValue());
        //管辖机构信息从证件档案表读取,人员档案表中的管辖机构用于历史多头管理需求，以证件表管辖机构为准
        PracCertificate pracCertificate = pracCertificateService.getValidPracCertificateByIdCertNum(pracApplyCommonInfoDTO.getIdCertNum(),
                EnumPracCertType.Normal.getValue(), pracApplyCommonInfoDTO.getAreaCode());
        Assert.notNull(pracCertificate, "从业人员证件信息为空！请联系管理员处理数据");
        pracApplyCommonInfoDTO.setOrgId(pracCertificate.getOrgId());
        SysOrganize sysOrganize = sysOrganizeService.getById(pracCertificate.getOrgId());
        Assert.notNull(sysOrganize, "从业人员管辖机构为空！请联系管理员处理数据");

        pracApplyCommonInfoDTO.setDepId(userInfo.getDepId());
        pracApplyCommonInfoDTO.setDepName(userInfo.getDepName());
        pracApplyCommonInfoDTO.setOrgName(sysOrganize.getOrgName());
        pracApplyCommonInfoDTO.setAreaCode(sysOrganize.getAreaCode());
        pracApplyCommonInfoDTO.setCreator(userInfo.getUserName());
        pracApplyCommonInfoDTO.setModifier(userInfo.getUserName());
        pracApplyCommonInfoDTO.setCreateTime(new Date());
        pracApplyCommonInfoDTO.setModifyTime(new Date());

        pracApplyCommonInfoDTO.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        pracApplyCommonInfoDTO.setDecisionResult(EnumDecisionResult.None.getValue());
        pracApplyCommonInfoDTO.setOperator(userInfo.getUserName());

        Integer applyType = pracApplyCommonInfoDTO.getApplyType();

        //业务验证
        // 验证年龄限制
        VerifyResultDTO verifyResultDTO = new VerifyResultDTO();
        if (EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getPracApplyBase().getSex()) || EmptyUtil.isEmpty(pracApplyCommonInfoDTO.getPracApplyBase().getBirthday())) {
            verifyResultDTO.setSuccess(false);
            verifyResultDTO.setErrorMsg("性别和出生日期不可为空");
            return;
        }

        String orgId = sysOrganize.getOrgId();
        if (EmptyUtil.isNotEmpty(orgId)) {
            // ## 3.未退休
            Integer minAge = practitionerService.retiredAge(orgId, pracApplyCommonInfoDTO.getPracTypeCodes(), pracApplyCommonInfoDTO.getPracApplyBase().getSex());
            DateTime dtRetirementAge = DateUtil.offset(pracApplyCommonInfoDTO.getPracApplyBase().getBirthday(), DateField.YEAR, minAge);
            Assert.isTrue(dtRetirementAge.isAfter(new Date()), "从业人员[" + pracApplyCommonInfoDTO.getPracName() + "]年龄已超过" + minAge + "岁!！");
        }

        // 验证证件是否为有效状态
        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();
        Assert.notNull(pracApplyCertDTO, "从业资格证件申请为空!");
        List<PracCertificate> pracCertificateList =
                pracCertificateService.list(Wrappers.<PracCertificate>lambdaQuery().
                        eq(PracCertificate::getPracId, pracApplyCommonInfoDTO.getPracId()).
                        eq(PracCertificate::getCertType, pracApplyCertDTO.getCertType()).
                        eq(PracCertificate::getCertState, EnumCertificateState.Valid.getValue()));
        Assert.isTrue(pracCertificateList.size() > 0, "从业资格证无有效记录，请联系数据部门处理!");
        Assert.isTrue(pracCertificateList.size() == 1, "从业资格证有多条相同证件类型的有效记录，请联系数据部门处理!");

        // 根据申请类型去执行到网上申请或者是自动归档，自动归档备注有相应的说明
        if ((applyType.equals(EnumPracApplyType.TransferOut.getValue())
                || applyType.equals(EnumPracApplyType.TransferIn.getValue()))
                && pracApplyCommonInfoDTO.getSource().equals(EnumApplySource.OneThing.getValue())) {
            pracApplyCommonInfoDTO.setState(EnumApplyState.Completed.getValue());
            pracApplyCommonInfoDTO.setCheckState(EnumCheckState.Passed.getValue());
            pracApplyCommonInfoDTO.setComments("普货人员资格证转出满足验证规则系统自动办结");
        } else if (applyType.equals(EnumPracApplyType.TransferOut.getValue())) {
            pracApplyCommonInfoDTO.setState(EnumApplyState.Submit.getValue());
        }

        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        PracApply pracApply = pracApplyMapStruct.toSource(pracApplyDTO);
        pracApplyService.save(pracApply);

        //归档转出申请表
        PracApplyTransferOutDTO pracApplyTransferOutDTO = pracApplyCommonInfoDTO.getPracApplyTransferOut();
        PracApplyTransferOut pracApplyTransferOut = pracApplyTransferOutMapStruct.toSource(pracApplyTransferOutDTO);
        pracApplyTransferOut.setTransferOutOrgName(sysOrganize.getOrgName());
        pracApplyTransferOut.setApplyId(pracApply.getApplyId());
        pracApplyTransferOutService.save(pracApplyTransferOut);

        //2.归档从业资格申请基础表
        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        PracApplyBase pracApplyBase = pracApplyBaseMapStruct.toSource(pracApplyBaseDTO);
        pracApplyBase.setApplyId(pracApply.getApplyId());
        pracApplyBase.setCreateTime(new Date());
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.save(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.归档从业资格申请基础表
        PracApplyCert pracApplyCert = pracApplyCertMapStruct.toSource(pracApplyCertDTO);
        pracApplyCert.setApplyId(pracApply.getApplyId());
        pracApplyCertService.save(pracApplyCert);

        //4.材料信息，先把之前提交的材料删除，以新提交的材料清单重新入库
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        pracApplyMaterialService.savePracApplyMaterial(pracApplyMaterials, pracApply.getApplyId());

        // 5.归档申请人邮寄信息
        if (Objects.equals(pracApplyDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.save(spBusinessApplyRecipient);
        }

        // 案号生成
        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        if ((applyType.equals(EnumPracApplyType.TransferOut.getValue())
                || applyType.equals(EnumPracApplyType.TransferIn.getValue()))
                && pracApplyCommonInfoDTO.getSource().equals(EnumApplySource.OneThing.getValue())) {
            //1.归档从业人员档案信息
            Practitioner practitioner = practitionerService.getById(pracApply.getPracId());
            Assert.notNull(practitioner, "从业人员档案信息不可为空！");

            //2.归档从业人员资格证信息
            pracCertificate.setCertState(EnumCertificateState.Invalid.getValue());
            pracCertificate.setPracState(EnumPractitionerState.TransferOut.getValue());
            pracCertificateService.update(pracCertificate);

            // 清除从业人员的所有从业资格(保留教练员的资格)
            //2.删除从业资格
            pracQualificationService.deletePracQualificationByPracId(practitioner.getPracId(), pracCertificate.getPracCertId());

            //从档案中移除转出的从业资格代码和名称
            String remainingCodes = String.join(",", CollectionUtil.subtractToList(Arrays.asList(practitioner.getPracTypeCodes().split(",")), Arrays.asList(pracCertificate.getPracTypeCodes().split(","))));
            String remainingNames = String.join(",", CollectionUtil.subtractToList(Arrays.asList(practitioner.getPracTypeNames().split(",")), Arrays.asList(pracCertificate.getPracTypeNames().split(","))));
            practitioner.setPracTypeCodes(remainingCodes);
            practitioner.setPracTypeNames(remainingNames);

            practitioner.setDrivingLicense(pracApplyBase.getDrivingLicense());
            practitioner.setDrivingVehicle(pracApplyBase.getDrivingVehicle());
            practitioner.setDrivLiceFirstDate(pracApplyBase.getDrivLiceFirstDate());
            practitioner.setDrivLiceStartDate(pracApplyBase.getDrivLiceStartDate());
            practitioner.setDrivLiceEndDate(pracApplyBase.getDrivLiceEndDate());
            practitioner.setDrivLicePeriod(pracApplyBase.getDrivLicePeriod());
            practitioner.setDrivLiceOrg(pracApplyBase.getDrivLiceOrg());
            practitioner.setPartyMemberStatus(pracApplyBase.getPartyMemberStatus());
            practitioner.setVeteransStatus(pracApplyBase.getVeteransStatus());

            //是否在岗
            practitioner.setOnJob(EnumPracIsOnJob.NotOnJob.getValue());
            practitioner.setNotJobDate(new Date());
            practitionerService.update(practitioner);

            //未完成的继续教育设置为无效
            pracEducationService.setPracEducationStateInvalid(practitioner.getPracId());

            //修改备案表信息 撤销备案
            pracRegisterApplyService.addPracRegisterCancelApplyToArchived(practitioner.getPracId(), "转出时自动撤销备案", pracApplyCert.getCertType(),
                    null, userInfo, pracApply);

            //重置从业人员基本信息中从业资格类型名称和代码
            resetPracTypeNameAndCode(practitioner.getPracId());
        } else if (applyType.equals(EnumPracApplyType.TransferOut.getValue())) {
            //8.发布事件
            applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Created));

        }
    }
}
