package com.guanwei.tmis.practitioner.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.tmis.common.dto.SPBusinessApplyRecipientDTO;
import com.guanwei.tmis.common.entity.SPBusinessApplyRecipient;
import com.guanwei.tmis.common.entity.SysOrganize;
import com.guanwei.tmis.common.enums.*;
import com.guanwei.tmis.common.mapstruct.SPBusinessApplyRecipientMapStruct;
import com.guanwei.tmis.common.service.CodeRuleService;
import com.guanwei.tmis.common.service.ComBsVerifyService;
import com.guanwei.tmis.common.service.SPBusinessApplyRecipientService;
import com.guanwei.tmis.common.service.SysOrganizeService;
import com.guanwei.tmis.common.utils.WorkflowUtils;
import com.guanwei.tmis.practitioner.common.dto.*;
import com.guanwei.tmis.practitioner.common.dto.mapstruct.*;
import com.guanwei.tmis.practitioner.common.entity.*;
import com.guanwei.tmis.practitioner.common.event.PracApplyEvent;
import com.guanwei.tmis.practitioner.common.service.*;
import com.guanwei.tmis.practitioner.service.PracApplyReplenishService;
import com.guanwei.token.model.UserInfo;
import com.guanwei.workflow.dto.WorkflowProcessDataDTO;
import com.guanwei.workflow.model.WorkflowVar;
import com.guanwei.workflow.service.WorkflowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class PracApplyReplenishServiceImpl implements PracApplyReplenishService {

    private final ApplicationContext applicationContext;

    private final PracApplyCommonInfoDTOMapStruct pracApplyBaseInfoOutMapStruct;

    private final PracApplyMapStruct pracApplyMapStruct;

    private final PracApplyService pracApplyService;

    private final PracApplyBaseService pracApplyBaseService;

    private final PracApplyBaseMapStruct pracApplyBaseMapStruct;

    private final PracApplyCertMapStruct pracApplyCertMapStruct;

    private final PracApplyCertService pracApplyCertService;

    private final PracApplyMaterialService pracApplyMaterialService;

    private final PracAttachmentMapStruct pracAttachmentMapStruct;

    private final PracAttachmentService pracAttachmentService;

    private final SPBusinessApplyRecipientMapStruct spBusinessApplyRecipientMapStruct;

    private final SPBusinessApplyRecipientService spBusinessApplyRecipientService;

    private final WorkflowService workflowService;

    private final PractitionerService practitionerService;

    private final PracCertificateService pracCertificateService;

    private final SysOrganizeService sysOrganizeService;

    private final ComBsVerifyService comBsVerifyService;

    private final CodeRuleService codeRuleService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submit(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        log.info("{},提交的信息:PracApplyCommonInfoDTO={},UserInfo={}", businessName(), pracApplyCommonInfoDTO, userInfo);

        //业务验证
        comBsVerifyService.bsCommonVerify(pracApplyCommonInfoDTO,
                EnumIndustryClass.Practitioner.getValue(), "0",
                pracApplyCommonInfoDTO.getApplyType(), pracApplyCommonInfoDTO.getOrgId());

        //1.归档从业资格申请表
        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        PracApply pracApply = pracApplyMapStruct.toSource(pracApplyDTO);
        pracApply.setState(EnumApplyState.Accepted.getValue());
        //TODO 申请理由类别
        pracApply.setReasonType(EnumPracReasonType.None.getValue());
        pracApply.setDecisionResult(EnumDecisionResult.None.getValue());
        pracApply.setCheckState(EnumCheckState.None.getValue());

        //受理信息
        pracApply.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        pracApply.setAccepter(userInfo.getUserName());
        pracApply.setAccepterId(userInfo.getUserId());
        pracApply.setAcceptDate(new Date());
        pracApply.setAcceptOrgId(userInfo.getOrgId());
        pracApply.setAcceptOrgName(userInfo.getOrgName());

        //管辖信息
        pracApply.setDepId(userInfo.getDepId());
        pracApply.setDepName(userInfo.getDepName());
        pracApply.setOrgId(userInfo.getOrgId());
        pracApply.setOrgName(userInfo.getOrgName());

        //经办人
        pracApply.setOperator(userInfo.getUserName());
        pracApply.setTelephone(userInfo.getMobile());
        pracApply.setMobile(userInfo.getMobile());

        //基础信息
        pracApply.setCreator(userInfo.getUserName());
        pracApply.setCreateTime(new Date());
        pracApply.setModifier(userInfo.getUserName());
        pracApply.setModifyTime(new Date());
        pracApply.setAreaCode(userInfo.getAreaCode());
        pracApply.setSource(EnumApplySource.WindowAccept.getValue());
        pracApplyService.save(pracApply);

        //2.归档从业资格申请基础表
        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        PracApplyBase pracApplyBase = pracApplyBaseMapStruct.toSource(pracApplyBaseDTO);
        pracApplyBase.setApplyId(pracApply.getApplyId());
        pracApplyBase.setCreateTime(new Date());
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.save(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.归档从业资格申请基础表,来自已有资格证信息（换证时证件信息不动）
        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();
        PracApplyCert pracApplyCert = pracApplyCertMapStruct.toSource(pracApplyCertDTO);
        pracApplyCertService.save(pracApplyCert);

        //6.归档从业资格申请材料清单表,申请附件表
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        if (EmptyUtil.isNotEmpty(pracApplyMaterials)) {
            for (PracApplyMaterialExDTO pracApplyMaterialExDTO : pracApplyMaterials) {
                //材料清单
                PracApplyMaterial pracApplyMaterial = new PracApplyMaterial();
                pracApplyMaterial.setApplyMaterialId(pracApplyMaterialExDTO.getApplyMaterialId());
                pracApplyMaterial.setMaterialCode(pracApplyMaterialExDTO.getMaterialCode());
                pracApplyMaterial.setMaterialName(pracApplyMaterialExDTO.getMaterialName());
                pracApplyMaterial.setMaterialType(pracApplyMaterialExDTO.getMaterialType());
                pracApplyMaterial.setOrderNo(pracApplyMaterialExDTO.getOrderNo());
                pracApplyMaterial.setCreateTime(new Date());
                pracApplyMaterial.setApplyId(pracApply.getApplyId());
                pracApplyMaterialService.saveOrUpdate(pracApplyMaterial);

                //材料清单-对应的附件
                List<PracAttachmentDTO> pracAttachments = pracApplyMaterialExDTO.getAttachs();
                if (EmptyUtil.isNotEmpty(pracAttachments)) {
                    List<PracAttachment> list = new ArrayList<>();
                    for (PracAttachmentDTO pracAttachmentDTO : pracAttachments) {
                        PracAttachment pracAttachment = pracAttachmentMapStruct.toSource(pracAttachmentDTO);
                        pracAttachment.setFileName(pracAttachmentDTO.getFileName());
                        pracAttachment.setApplyMaterialId(pracApplyMaterial.getApplyMaterialId());
                        list.add(pracAttachment);
                    }
                    pracAttachmentService.saveOrUpdateBatch(list);
                }
            }
        }

        //8.归档申请人邮寄信息
        if (EnumYesOrNot.Yes.getValue().equals(pracApplyCommonInfoDTO.getIsMail())) {
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.save(spBusinessApplyRecipient);
        }

        // 案号生成
        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //9.工作流
        StringBuilder bizObjectName = new StringBuilder(pracApplyDTO.getPracName());
        if (EmptyUtil.isNotEmpty(pracApply.getIdCertNum())) {
            bizObjectName.append(MessageFormat.format("（身份证：{0}）", pracApplyDTO.getIdCertNum()));
        }
        if (EmptyUtil.isNotEmpty(pracApplyCert.getArchiveNum())) {
            bizObjectName.append(MessageFormat.format("（档案号：{0}）", pracApplyCert.getArchiveNum()));
        }
        workflowService.startWorkflow(WorkflowUtils.convertToWorkflowUser(userInfo), pracApply.getApplyId(), pracApply.getBusinessNo(), bizObjectName, pracApplyCommonInfoDTO.getProcessData());
        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //10.发布事件
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Created));

    }

    /**
     * 业务受理操作
     *
     * @param pracApplyCommonInfoDTO 进行应用首先添加信息dto
     * @param userInfo               用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void accept(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        log.info("{},提交的信息:PracApplyCommonInfoDTO={},UserInfo={}", businessName(), pracApplyCommonInfoDTO, userInfo);
        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        Assert.notNull(pracApplyDTO, "申请信息不存在！");

        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        Assert.notNull(pracApplyBaseDTO, "申请基础信息不存在！");

        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();
        Assert.notNull(pracApplyCertDTO, "申请证件信息不存在！");

        //1.更新申请表
        String applyId = pracApplyDTO.getApplyId();
        PracApply pracApply = pracApplyService.getById(applyId);
        Assert.notNull(pracApply, "Id为【" + applyId + "】的从业人员申请信息已不存在！");

        //业务验证
        comBsVerifyService.bsCommonVerify(pracApplyCommonInfoDTO,
                EnumIndustryClass.Practitioner.getValue(), "0",
                pracApplyCommonInfoDTO.getApplyType(), pracApplyCommonInfoDTO.getOrgId());

        pracApplyMapStruct.updateToSource(pracApplyDTO, pracApply);

        //受理信息
        pracApply.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        if (EmptyUtil.isEmpty(pracApply.getAccepterId())) {
            pracApply.setAccepterId(userInfo.getUserId());
        }
        if (EmptyUtil.isEmpty(pracApply.getAccepter())) {
            pracApply.setAccepter(userInfo.getUserName());
        }
        if (EmptyUtil.isEmpty(pracApply.getAcceptDate())) {
            pracApply.setAcceptDate(new Date());
        }
        if (EmptyUtil.isEmpty(pracApply.getAcceptOrgId())) {
            pracApply.setAcceptOrgId(userInfo.getOrgId());
        }
        if (EmptyUtil.isEmpty(pracApply.getAcceptOrgName())) {
            pracApply.setAcceptOrgName(userInfo.getOrgName());
        }

        pracApply.setState(EnumApplyState.Accepted.getValue());
        pracApply.setModifier(userInfo.getUserName());
        pracApply.setModifyTime(new Date());
        pracApplyService.updateById(pracApply);

        //2.申请基础信息
        PracApplyBase pracApplyBase = pracApplyBaseService.getById(applyId);
        Assert.notNull(pracApplyBase, "Id为【" + applyId + "】的从业人员申请基础信息不存在！");
        pracApplyBaseMapStruct.updateToSource(pracApplyBaseDTO, pracApplyBase);
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.updateById(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.证件信息
        PracApplyCert pracApplyCert = pracApplyCertService.getById(applyId);
        Assert.notNull(pracApplyCert, "Id为【" + applyId + "】的从业人员申请证件信息不存在！");
        pracApplyCertMapStruct.updateToSource(pracApplyCertDTO, pracApplyCert);
        pracApplyCertService.updateById(pracApplyCert);

        //4.材料信息，先把之前提交的材料删除，以新提交的材料清单重新入库
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        pracApplyMaterialService.savePracApplyMaterial(pracApplyMaterials, pracApply.getApplyId());

        //5.邮寄信息，先把之前提交的删除，以新提交的数据重新入库
        //先删除
        spBusinessApplyRecipientService.remove(Wrappers.lambdaQuery(SPBusinessApplyRecipient.class).eq(SPBusinessApplyRecipient::getApplyId, applyId));
        if (Objects.equals(pracApplyDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            //归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.save(spBusinessApplyRecipient);
        }

        Integer acceptResult = pracApply.getAcceptResult();
        //如果不予受理，办结
        if (Objects.equals(EnumAcceptResult.NotAccepted.getValue(), acceptResult)) {
            LambdaUpdateWrapper<PracApply> lambdaUpdateCompletedWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateCompletedWrapper.set(PracApply::getState, EnumApplyState.Completed.getValue());
            lambdaUpdateCompletedWrapper.set(PracApply::getModifier, userInfo.getUserName());
            lambdaUpdateCompletedWrapper.set(PracApply::getModifyTime, new Date());
            lambdaUpdateCompletedWrapper.eq(PracApply::getApplyId, pracApply.getApplyId());
            pracApplyService.update(lambdaUpdateCompletedWrapper);
        }

        List<WorkflowVar> workflowVars = Lists.newArrayList();
        WorkflowProcessDataDTO workflowProcessDataDTO = pracApplyCommonInfoDTO.getProcessData();
        workflowService.submitWorkflow(workflowProcessDataDTO, workflowVars, WorkflowUtils.convertToWorkflowUser(userInfo));

        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //9.发布事件
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Accepted));

    }

    /**
     * 微信端初领增类受理-发起流程
     *
     * @param pracApplyCommonInfoDTO 申请换证的信息dto
     * @param userInfo               用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void wxAccept(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, UserInfo userInfo) {
        PracApplyDTO pracApplyDTO = pracApplyBaseInfoOutMapStruct.toPracApplyDTO(pracApplyCommonInfoDTO);
        Assert.notNull(pracApplyDTO, "申请信息不存在！");

        PracApplyBaseDTO pracApplyBaseDTO = pracApplyCommonInfoDTO.getPracApplyBase();
        Assert.notNull(pracApplyBaseDTO, "申请基础信息不存在！");

        PracApplyCertDTO pracApplyCertDTO = pracApplyCommonInfoDTO.getPracApplyCert();
        Assert.notNull(pracApplyCertDTO, "申请证件信息不存在！");

        //1.更新申请表
        String applyId = pracApplyDTO.getApplyId();
        PracApply pracApply = pracApplyService.getById(applyId);
        Assert.notNull(pracApply, "Id为【" + applyId + "】的从业人员申请信息已不存在！");

        //业务验证
        comBsVerifyService.bsCommonVerify(pracApplyCommonInfoDTO,
                EnumIndustryClass.Practitioner.getValue(), "0",
                pracApplyCommonInfoDTO.getApplyType(), pracApplyCommonInfoDTO.getOrgId());

        pracApplyMapStruct.updateToSource(pracApplyDTO, pracApply);
        pracApply.setState(EnumApplyState.Accepted.getValue());
        pracApply.setModifier(userInfo.getUserName());
        pracApply.setModifyTime(new Date());

        //受理信息
        pracApply.setAcceptResult(EnumAcceptResult.Accepted.getValue());
        pracApply.setAccepter(userInfo.getUserName());
        pracApply.setAccepterId(userInfo.getUserId());
        pracApply.setAcceptDate(new Date());
        pracApply.setAcceptOrgId(userInfo.getOrgId());
        pracApply.setAcceptOrgName(userInfo.getOrgName());

        //管辖信息
        pracApply.setDepId(userInfo.getDepId());
        pracApply.setDepName(userInfo.getDepName());
        pracApply.setOrgId(userInfo.getOrgId());
        pracApply.setOrgName(userInfo.getOrgName());
        pracApply.setModifier(userInfo.getUserName());
        pracApply.setModifyTime(new Date());
        pracApplyService.updateById(pracApply);

        //2.申请基础信息
        PracApplyBase pracApplyBase = pracApplyBaseService.getById(applyId);
        Assert.notNull(pracApplyBase, "Id为【" + applyId + "】的从业人员申请基础信息不存在！");
        pracApplyBaseMapStruct.updateToSource(pracApplyBaseDTO, pracApplyBase);
        pracApplyBase.setModifyTime(new Date());
        pracApplyBaseService.updateById(pracApplyBase);
        pracApplyBaseService.resetDrivLiceEndDate(pracApply.getApplyId(), pracApplyBase);

        //3.证件信息
        PracApplyCert pracApplyCert = pracApplyCertService.getById(applyId);
        Assert.notNull(pracApplyCert, "Id为【" + applyId + "】的从业人员申请证件信息不存在！");
        pracApplyCertMapStruct.updateToSource(pracApplyCertDTO, pracApplyCert);
        pracApplyCertService.updateById(pracApplyCert);

        //4.材料信息，先把之前提交的材料删除，以新提交的材料清单重新入库
        List<PracApplyMaterialExDTO> pracApplyMaterials = pracApplyCommonInfoDTO.getMaterials();
        pracApplyMaterialService.savePracApplyMaterial(pracApplyMaterials, pracApply.getApplyId());

        //5.邮寄信息，先把之前提交的删除，以新提交的数据重新入库
        //先删除
        spBusinessApplyRecipientService.remove(Wrappers.lambdaQuery(SPBusinessApplyRecipient.class).eq(SPBusinessApplyRecipient::getApplyId, applyId));
        if (Objects.equals(pracApplyDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            //归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = pracApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(pracApply.getApplyId());
            spBusinessApplyRecipientService.save(spBusinessApplyRecipient);
        }

        Integer acceptResult = pracApply.getAcceptResult();
        //如果不予受理，办结
        if (Objects.equals(EnumAcceptResult.NotAccepted.getValue(), acceptResult)) {
            LambdaUpdateWrapper<PracApply> lambdaUpdateCompletedWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateCompletedWrapper.set(PracApply::getState, EnumApplyState.Completed.getValue());
            lambdaUpdateCompletedWrapper.set(PracApply::getModifier, userInfo.getUserName());
            lambdaUpdateCompletedWrapper.set(PracApply::getModifyTime, new Date());
            lambdaUpdateCompletedWrapper.eq(PracApply::getApplyId, pracApply.getApplyId());
            pracApplyService.update(lambdaUpdateCompletedWrapper);
        }

        //9.工作流
        pracApplyCommonInfoDTO.getProcessData().setProcessId("00103");
        StringBuilder bizObjectName = new StringBuilder(pracApplyDTO.getPracName());
        if (EmptyUtil.isNotEmpty(pracApply.getIdCertNum())) {
            bizObjectName.append(MessageFormat.format("（身份证：{0}）", pracApplyDTO.getIdCertNum()));
        }
        if (EmptyUtil.isNotEmpty(pracApplyCert.getArchiveNum())) {
            bizObjectName.append(MessageFormat.format("（档案号：{0}）", pracApplyCert.getArchiveNum()));
        }
        workflowService.startWorkflow(WorkflowUtils.convertToWorkflowUser(userInfo), pracApply.getApplyId(), pracApply.getBusinessNo(), bizObjectName, pracApplyCommonInfoDTO.getProcessData());

        if (EmptyUtil.isEmpty(pracApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(userInfo.getOrgId(), CodeRuleService.CASE_NO_PRAC, true);
            pracApply.setBusinessNo(docCaseNo);
            pracApplyService.update(pracApply);
        }

        //9.发布事件
        applicationContext.publishEvent(new PracApplyEvent(pracApply.getApplyId(), EnumPracApplyEvent.Accepted));

    }

    /**
     * 归档-补证
     *
     * @param pracApply     从业资格申请信息
     * @param pracApplyBase 从业资格申请基础信息
     * @param pracApplyCert 从业资格申请证件信息
     * @param userInfo      用户信息
     */
    @Override
    public void archive(PracApply pracApply, PracApplyBase pracApplyBase, PracApplyCert pracApplyCert, UserInfo userInfo) {
        Practitioner practitioner = practitionerService.getOne(pracApply.getPracId());
        Assert.notNull(practitioner, "Id为【" + pracApply.getPracId() + "】的从业人员档案信息已不存在！");

        //************************************* 申请数据   ***************************************
        SysOrganize issueOrganize = sysOrganizeService.getIssueOrganize(pracApply.getOrgId(),
                EnumIndustryClass.Practitioner.getValue());
        Assert.notNull(issueOrganize, "未计算得出发证机构信息！");

        pracApplyCert.setIssueOrgId(issueOrganize.getOrgId());
        pracApplyCert.setIssueOrgName(issueOrganize.getOrgName());
        pracApplyCertService.update(pracApplyCert);

        //************************************* 档案数据   ***************************************
        //1.归档从业人员档案信息
        practitioner.setPracName(pracApply.getPracName());
        practitioner.setSex(pracApplyBase.getSex());
        practitioner.setBirthday(pracApplyBase.getBirthday());
        practitioner.setNationality(pracApplyBase.getNationality());
        practitioner.setIdCertType(pracApply.getIdCertType());
        practitioner.setIdCertNum(pracApply.getIdCertNum());
        practitioner.setCounty(pracApplyBase.getCounty());
        practitioner.setPhoneNum(pracApplyBase.getPhoneNum());
        practitioner.setMobile(pracApplyBase.getMobile());
        practitioner.setIdCertAddress(pracApplyBase.getIdCertAddress());
        practitioner.setAddress(pracApplyBase.getAddress());
        practitioner.setEmail(pracApplyBase.getEmail());
        practitioner.setPostcode(pracApplyBase.getPostcode());
        practitioner.setEducation(pracApplyBase.getEducation());
        practitioner.setTechnicalPost(pracApplyBase.getTechnicalPost());
        practitioner.setHealthState(pracApplyBase.getHealthState());
        practitioner.setDrivingLicense(pracApplyBase.getDrivingLicense());
        practitioner.setDrivingVehicle(pracApplyBase.getDrivingVehicle());
        practitioner.setDrivLiceFirstDate(pracApplyBase.getDrivLiceFirstDate());
        practitioner.setDrivLiceStartDate(pracApplyBase.getDrivLiceStartDate());
        practitioner.setDrivLiceEndDate(pracApplyBase.getDrivLiceEndDate());
        practitioner.setDrivLicePeriod(pracApplyBase.getDrivLicePeriod());
        practitioner.setDrivLiceOrg(pracApplyBase.getDrivLiceOrg());
        practitioner.setDrivArchiveNum(pracApplyBase.getDrivArchiveNum());
        practitioner.setArchiveNum(pracApplyCert.getArchiveNum());
        practitioner.setPhotoPath(pracApplyBase.getPhotoPath());
        practitioner.setPracTypeNames(pracApply.getPracTypeNames());
        practitioner.setPracTypeCodes(pracApply.getPracTypeCodes());
        practitioner.setTeachVehicleFirstDate(pracApplyBase.getTeachVehicleFirstDate());
        practitioner.setSafeDriving(pracApplyBase.getSafeDriving());
        practitioner.setAreaCode(pracApply.getAreaCode());
        practitioner.setOrgId(pracApply.getOrgId());
        // practitioner.setCreateTime(new Date());
        // practitioner.setCreator(userInfo.getUserName());
        practitioner.setModifier(userInfo.getUserName());
        practitioner.setModifyTime(new Date());
        practitioner.setComments(pracApply.getComments());
        practitioner.setOnJob(EnumPracIsOnJob.NotOnJob.getValue());
        practitioner.setMaritalStatus(pracApplyBase.getMaritalStatus());
        practitioner.setForeignLanguageAbility(pracApplyBase.getForeignLanguageAbility());
        practitioner.setIfLocalPeople(pracApplyBase.getIfLocalPeople());
        practitioner.setCitizenship(pracApplyBase.getCitizenship());
        practitioner.setPartyMemberStatus(pracApplyBase.getPartyMemberStatus());
        practitioner.setVeteransStatus(pracApplyBase.getVeteransStatus());
        practitionerService.update(practitioner);

        //2.归档从业人员资格证信息
        LambdaQueryWrapper<PracCertificate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PracCertificate::getPracId, pracApply.getPracId());
        lambdaQueryWrapper.eq(PracCertificate::getCertType, pracApplyCert.getCertType());
        lambdaQueryWrapper.eq(PracCertificate::getCertState, EnumCertificateState.Valid.getValue());
        PracCertificate pracCertificate = pracCertificateService.getOne(lambdaQueryWrapper, false);
        Assert.notNull(pracCertificate, "Id为【" + pracApply.getPracId() + "】的从业人员证件信息已不存在！");

        pracCertificate.setCounty(pracApplyBase.getCounty());
        pracCertificate.setArchiveNum(pracApplyCert.getArchiveNum());
        pracCertificate.setCertNum(pracApplyCert.getCertNum());
        pracCertificate.setCertType(pracApplyCert.getCertType());
        pracCertificate.setPracCertNum(pracApplyCert.getPracCertNum());
        pracCertificate.setTeachingVehicles(pracApplyBase.getTeachingVehicles());
        pracCertificate.setPracCertStartDate(pracApplyCert.getPracCertStartDate());
        pracCertificate.setPracCertIssueDate(new Date());
        pracCertificate.setPracCertEndDate(pracApplyCert.getPracCertEndDate());
        pracCertificate.setOrgId(pracApply.getOrgId());
        pracCertificate.setIssueOrgName(pracApplyCert.getIssueOrgName());
        pracCertificate.setIssueOrgId(pracApplyCert.getIssueOrgId());
        pracCertificate.setAreaCode(pracApply.getAreaCode());
        pracCertificate.setPracCertPhotoPath(pracApplyCert.getPracCertPhotoPath());
        pracCertificate.setVerifyCode(pracApplyCert.getVerifyCode());
        pracCertificate.setICCardNum(pracApplyCert.getIcCardNum());
        pracCertificate.setCardTime(pracApplyCert.getCardTime());
        pracCertificate.setPracId(practitioner.getPracId());
        pracCertificateService.update(pracCertificate);

        //重置从业人员基本信息中从业资格类型名称和代码
        resetPracTypeNameAndCode(practitioner.getPracId());
    }

}
