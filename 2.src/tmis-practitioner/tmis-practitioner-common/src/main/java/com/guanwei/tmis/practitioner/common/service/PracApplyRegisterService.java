package com.guanwei.tmis.practitioner.common.service;

import com.guanwei.mybatis.base.service.MBaseService;
import com.guanwei.tmis.practitioner.common.dto.PracApplyCommonInfoDTO;
import com.guanwei.tmis.practitioner.common.dto.PracApplyRegisterDTO;
import com.guanwei.tmis.practitioner.common.dto.PracExperienceSimpleDTO;
import com.guanwei.tmis.practitioner.common.entity.PracApplyRegister;

import java.util.List;

/**
 * PracApplyRegisterService 服务接口
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
public interface PracApplyRegisterService extends MBaseService<PracApplyRegister> {

    /**
     * 出租车车辆从业资格取消注册
     *
     * @param vehicleNum 车牌号码
     * @param plateColor 车牌颜色
     */
    void taxiVehiclePracCancelRegister(String vehicleNum, Integer plateColor);

    /**
     * 注销备案归档
     *
     * @param pracApplyRegister 进行申请注册
     * @param pracId         人员标识
     * <AUTHOR>
     * @since 2022/10/13 11:47:19
     */
    void pracRegisterApplyArchivedByCancel(PracApplyRegister pracApplyRegister, String pracId, String areaCode);

    /**
     * 根据身份证号查询从业人员从业经历
     *
     * @param idCertNum 身份证号
     * @return {@link List }<{@link PracApplyRegisterDTO }>
     * <AUTHOR>
     * @since 2022/11/01 16:52:02
     */
    List<PracApplyRegisterDTO> getPracRegisterApplyHisListByIdCertNum(String idCertNum);

    /**
     * 根据身份证获取从业经历
     *
     * @param idCertNum id证书编号
     * @return {@link List }<{@link PracExperienceSimpleDTO }>
     */
    List<PracExperienceSimpleDTO> getPracExperienceSimpleList(String idCertNum);

    /**
     * 根据从业人员编号查询备案详细信息
     *
     * @param pracId    从业人员id
     * <AUTHOR>
     * @since 2023/4/23 09:58:46
     */
    List<PracApplyRegisterDTO> getPracApplyRegisterInfo(String pracId);

    /**
     * 网约车-注销备案归档
     *
     * @param registerId 注册id
     */
    void pracRegisterApplyArchivedNetDriverByCancel(String registerId);

    /**
     * 验证是否黑名单
     * 备案
     *
     * @param pracApplyCommonInfoDTO 从业人员申请通用信息
     */
    void verifyAddSupervision(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, String orgId);

    /**
     * 验证是否允许外籍从业人员备案
     * 备案
     *
     * @param pracApplyCommonInfoDTO 从业人员申请通用信息
     */
    void verifyOutHometown(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, String orgId);

    /**
     * 验证是否已提交申请 (防止重复申请)
     * 备案
     *
     * @param pracApplyCommonInfoDTO 从业人员申请通用信息
     */
    void verifyHasRecordApply(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, String orgId);

    /**
     * 验证是否可以备案 1.未退休 2.证件在有效期内
     * 备案
     *
     * @param pracApplyCommonInfoDTO 从业人员申请通用信息
     */
    void verifyCanRecord(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, String orgId);

    /**
     * 验证是否可以注册 1.未退休 2.证件在有效期内
     * 巡游注册 网约注册
     *
     * @param pracApplyCommonInfo prac应用常见信息
     * @param orgId               组织id
     */
    void verifyCanNetRegister(PracApplyCommonInfoDTO pracApplyCommonInfo, String orgId);

    /**
     * 验证是否已经备案 1.是否允许同时存在网约注册、巡游注册、人员备案
     * 备案
     *
     * @param pracApplyCommonInfoDTO 从业人员申请通用信息
     */
    void verifyHadRegisterForRecord(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, String orgId);

    /**
     * 验证是否已经巡游注册 1.是否允许同时存在网约注册、巡游注册、人员备案
     * 巡游注册
     *
     * @param pracApplyCommonInfoDTO 从业人员申请通用信息
     */
    void verifyHadRegisterForTaxi(PracApplyCommonInfoDTO pracApplyCommonInfoDTO, String orgId);

    /**
     * 验证是否已经网约注册 1.是否允许同时存在网约注册、巡游注册、人员备案 2.能否多平台注册
     * 网约注册
     *
     * @param pracApplyCommonInfo prac应用常见信息
     * @param orgId               组织id
     */
    void verifyHadRegisterForNet(PracApplyCommonInfoDTO pracApplyCommonInfo, String orgId);

    /**
     * 验证车辆是否是企业下注册的网约车
     * 网约注册
     *
     * @param pracApplyCommonInfo prac应用常见信息
     * @param orgId               组织id
     */
    void verifyNetTaxiVehicleBelongOwner(PracApplyCommonInfoDTO pracApplyCommonInfo, String orgId);

    /**
     * 验证网约车上注册的人数
     * 网约注册
     *
     * @param pracApplyCommonInfo prac应用常见信息
     * @param orgId               机构ID
     */
    void verifyNetTaxiVehicleBoundNum(PracApplyCommonInfoDTO pracApplyCommonInfo, String orgId);

    /**
     * 验证人车比
     * 撤销备案
     *
     * @param pracApplyCommonInfo prac应用常见信息
     * @param orgId               机构ID
     */
    void verifyPersonToVehicleRatio(PracApplyCommonInfoDTO pracApplyCommonInfo, String orgId);

}
