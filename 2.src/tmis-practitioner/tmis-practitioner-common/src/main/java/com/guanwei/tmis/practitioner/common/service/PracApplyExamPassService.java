package com.guanwei.tmis.practitioner.common.service;

import com.guanwei.core.utils.result.R;
import com.guanwei.tmis.practitioner.common.dto.PracApplyExamPassDTO;
import com.guanwei.tmis.practitioner.common.dto.PracApplyExamPassInfoDTO;
import com.guanwei.tmis.practitioner.common.dto.PracApplyMaterialExDTO;
import com.guanwei.tmis.practitioner.common.query.PracApplyExamPassQuery;

import java.util.List;

/**
 * 进行应用考试信息服务
 *
 * <AUTHOR>
 * @date 2022/08/05
 */
public interface PracApplyExamPassService {

    /**
     * 获取指定的从业资格申请考试通过信息
     *
     * @param applyId 申请id
     * @return {@link PracApplyExamPassDTO}
     */
    PracApplyExamPassDTO getPracApplyExamPassList(String applyId);

    /**
     * 获取从业资格申请考试通过
     *
     * @param pracApplyExamPassQuery 从业资格申请考试通过查询
     * @return {@link List}<{@link PracApplyExamPassInfoDTO}>
     */
    List<PracApplyExamPassInfoDTO> getPracApplyExamPass(PracApplyExamPassQuery pracApplyExamPassQuery);

    /**
     * 添加从业资格申请考试通过的信息
     *
     * @param pracApplyExamPass 从业资格申请考试通过
     * @return 申请id
     */
    String addPracApplyExamPass(PracApplyExamPassDTO pracApplyExamPass);

    /**
     * 更新从业资格申请考试通过的信息
     *
     * @param pracApplyExamPass 从业资格申请考试通过
     * @param applyId           申请id
     */
    default void updatePracApplyExamPass(PracApplyExamPassDTO pracApplyExamPass, String applyId) {
    }

    /**
     * 初始化考试通过从业资格人申请信息
     *
     * @param certType  证书类型
     * @param idCertNum 身份证号码
     * @param applyType 申请类型
     * @return {@link PracApplyExamPassDTO}
     */
    PracApplyExamPassDTO initPracApplyExamPass(Integer certType, String idCertNum, Integer applyType);

    /**
     * 初始化-录入考试通过的申请需要提交的材料清单
     *
     * @param applyType    申请类型
     * @param pracTypeCode 从业资格类型代码
     * @return {@link R}<{@link ?}>
     */
    List<PracApplyMaterialExDTO> initPracApplyMaterials(Integer applyType, String pracTypeCode);

    /**
     * 作废考试合格的从业人员业务申请
     *
     * @param applyId  申请id
     * @param comments 备注
     */
    default void inValidPracApplyExamPass(String applyId, String comments) {
    }

    /**
     * 查询网约车考试合格的从业人员申请记录
     *
     * @param pracApplyExamPassQuery 页面请求
     * @return {@link R }<{@link List<PracApplyExamPassInfoDTO> }>
     * @since 2024/06/12
     */
    List<PracApplyExamPassInfoDTO> getNetListExamPass(PracApplyExamPassQuery pracApplyExamPassQuery);
}
