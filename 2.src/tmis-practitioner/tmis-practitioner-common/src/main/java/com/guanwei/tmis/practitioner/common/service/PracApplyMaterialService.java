package com.guanwei.tmis.practitioner.common.service;

import com.guanwei.mybatis.base.service.MBaseService;
import com.guanwei.tmis.practitioner.common.dto.PracApplyMaterialAiRecResult;
import com.guanwei.tmis.practitioner.common.dto.PracApplyMaterialExDTO;
import com.guanwei.tmis.practitioner.common.entity.PracApplyMaterial;

import java.util.List;

/**
 * PracApplyMaterialService 服务接口
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
public interface PracApplyMaterialService extends MBaseService<PracApplyMaterial> {

    /**
     * 保存从业人员申请材料
     *
     * @param pracApplyMaterials 业人员申请材料dto
     * @param applyId            业人员申请id
     * <AUTHOR>
     * @since 2022/10/13 10:19:17
     */
    void savePracApplyMaterial(List<PracApplyMaterialExDTO> pracApplyMaterials, String applyId);

    /**
     * 获取从业资格申请材料附件
     *
     * @param applyId 申请标识ID
     * @return {@link List}<{@link PracApplyMaterialExDTO}>
     */
    List<PracApplyMaterialExDTO> getPracApplyMaterialExDTO(String applyId);

    /**
     * AI 识别
     *
     * @param applyMaterialExDTO 将材料应用于 DTO
     * @return {@link PracApplyMaterialAiRecResult }
     */
    PracApplyMaterialAiRecResult aiRecognize(PracApplyMaterialExDTO applyMaterialExDTO);
}
