package com.guanwei.tmis.practitioner.common.service;

import com.guanwei.mybatis.base.service.MBaseService;
import com.guanwei.tmis.practitioner.common.dto.PracQualificationDTO;
import com.guanwei.tmis.practitioner.common.entity.PracQualification;

import java.util.List;

/**
 * PracQualificationService 服务接口
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
public interface PracQualificationService extends MBaseService<PracQualification> {


    /**
     * 通过从业人员Id删除资格类别
     *
     * @param pracId     进行唯一标识
     * @param pracCertId 进行cert唯一标识
     * <AUTHOR>
     * @since 2022/09/20 09:55:58
     */
    void deletePracQualificationByPracId(String pracId, String pracCertId);


    /**
     * 获取所有进行资格列表
     *
     * @param pracId 进行唯一标识
     * @return {@link List }<{@link PracQualificationDTO }>
     * <AUTHOR>
     * @since 2022/09/20 15:58:55
     */
    List<PracQualificationDTO> getAllPracQualificationList(String pracId);
}
