package com.guanwei.tmis.vehicle.common.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.mybatis.base.service.MBaseServiceImpl;
import com.guanwei.tmis.vehicle.common.dto.VehicleApplyPhotoDTO;
import com.guanwei.tmis.vehicle.common.dto.mapstruct.VehicleApplyPhotoMapStruct;
import com.guanwei.tmis.vehicle.common.entity.VehicleApplyPhoto;
import com.guanwei.tmis.vehicle.common.mapper.VehicleApplyPhotoMapper;
import com.guanwei.tmis.vehicle.common.service.VehicleApplyPhotoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车辆申请照片服务实现
 *
 * <AUTHOR>
 * @date 2023-10-10  11:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VehicleApplyPhotoServiceImpl extends MBaseServiceImpl<VehicleApplyPhotoMapper, VehicleApplyPhoto>
        implements VehicleApplyPhotoService {

    private final VehicleApplyPhotoMapStruct vehicleApplyPhotoMapStruct;

    /**
     * 保存车辆申请照片
     *
     * @param photos  照片
     * @param applyId 申请id
     */
    @Override
    public void saveVehicleApplyPhoto(List<VehicleApplyPhotoDTO> photos, String applyId) {
        if (EmptyUtil.isNotEmpty(photos)) {
            // 先移除
            this.remove(Wrappers.lambdaQuery(VehicleApplyPhoto.class).eq(VehicleApplyPhoto::getApplyId, applyId));
            // 再新增
            for (VehicleApplyPhotoDTO vehicleApplyPhotoDTO : photos) {
                //材料清单
                VehicleApplyPhoto vehicleApplyPhoto = vehicleApplyPhotoMapStruct.toSource(vehicleApplyPhotoDTO);
                vehicleApplyPhoto.setApplyId(applyId);
                vehicleApplyPhoto.setPhotoId(null);
                this.save(vehicleApplyPhoto);
            }
        }
    }
}
