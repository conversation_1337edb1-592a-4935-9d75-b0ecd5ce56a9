package com.guanwei.tmis.vehicle.common.service.impl;

import com.guanwei.mybatis.base.service.MBaseServiceImpl;
import com.guanwei.tmis.vehicle.common.entity.VehicleAttachment;
import com.guanwei.tmis.vehicle.common.mapper.VehicleAttachmentMapper;
import com.guanwei.tmis.vehicle.common.service.VehicleAttachmentService;
import org.springframework.stereotype.Service;

/**
 * VehicleAttachmentService  服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-14
 */
@Service
public class VehicleAttachmentServiceImpl extends MBaseServiceImpl<VehicleAttachmentMapper, VehicleAttachment> implements VehicleAttachmentService {

}
