package com.guanwei.tmis.vehicle.common.service.impl;

import com.guanwei.mybatis.base.service.MBaseServiceImpl;
import com.guanwei.tmis.vehicle.common.entity.VehicleBatchApply;
import com.guanwei.tmis.vehicle.common.mapper.VehicleBatchApplyMapper;
import com.guanwei.tmis.vehicle.common.service.VehicleBatchApplyService;
import org.springframework.stereotype.Service;

/**
 * VehicleBatchApplyService 服务实现类
 *
 * <AUTHOR>
 * @date 2024/12/02
 */
@Service
public class VehicleBatchApplyServiceImpl extends MBaseServiceImpl<VehicleBatchApplyMapper, VehicleBatchApply> implements VehicleBatchApplyService {
}
