package com.guanwei.tmis.vehicle.common.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.mybatis.base.service.MBaseServiceImpl;
import com.guanwei.tmis.vehicle.common.dto.VehicleApplyBusiScopeDTO;
import com.guanwei.tmis.vehicle.common.dto.mapstruct.VehicleApplyBusiScopeMapStruct;
import com.guanwei.tmis.vehicle.common.entity.VehicleApplyBusiScope;
import com.guanwei.tmis.vehicle.common.mapper.VehicleApplyBusiScopeMapper;
import com.guanwei.tmis.vehicle.common.service.VehicleApplyBusiScopeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车辆申请经营范围服务
 *
 * <AUTHOR>
 * @date 2023/10/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VehicleApplyBusiScopeServiceImpl extends MBaseServiceImpl<VehicleApplyBusiScopeMapper, VehicleApplyBusiScope>
        implements VehicleApplyBusiScopeService {

    private final VehicleApplyBusiScopeMapStruct vehicleApplyBusiScopeMapStruct;

    /**
     * 保存车辆申请经营范围
     *
     * @param busiScopes 经营范围
     * @param applyId    申请id
     */
    @Override
    public void saveVehicleApplyBusiScope(List<VehicleApplyBusiScopeDTO> busiScopes, String applyId) {
        if (EmptyUtil.isNotEmpty(busiScopes)) {
            // 先移除
            this.remove(Wrappers.lambdaQuery(VehicleApplyBusiScope.class).eq(VehicleApplyBusiScope::getApplyId, applyId));
            // 再新增
            for (VehicleApplyBusiScopeDTO vehicleApplyBusiScopeDTO : busiScopes) {
                //材料清单
                VehicleApplyBusiScope vehicleApplyBusiScope = vehicleApplyBusiScopeMapStruct.toSource(vehicleApplyBusiScopeDTO);
                vehicleApplyBusiScope.setApplyId(applyId);
                vehicleApplyBusiScope.setApplyBusiScopeId(null);
                this.save(vehicleApplyBusiScope);
            }
        }
    }
}
