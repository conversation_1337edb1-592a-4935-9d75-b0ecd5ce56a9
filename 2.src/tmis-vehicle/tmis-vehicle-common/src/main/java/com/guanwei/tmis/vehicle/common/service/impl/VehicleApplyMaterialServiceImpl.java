package com.guanwei.tmis.vehicle.common.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.guanwei.core.ApplicationContextHolder;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.mybatis.base.service.MBaseServiceImpl;
import com.guanwei.tmis.common.config.TmisPhotoPathWriter;
import com.guanwei.tmis.vehicle.common.dto.VehicleApplyMaterialExDTO;
import com.guanwei.tmis.vehicle.common.dto.VehicleAttachmentDTO;
import com.guanwei.tmis.vehicle.common.dto.mapstruct.VehicleApplyMaterialExMapStruct;
import com.guanwei.tmis.vehicle.common.dto.mapstruct.VehicleAttachmentMapStruct;
import com.guanwei.tmis.vehicle.common.entity.VehicleApplyMaterial;
import com.guanwei.tmis.vehicle.common.entity.VehicleAttachment;
import com.guanwei.tmis.vehicle.common.mapper.VehicleApplyMaterialMapper;
import com.guanwei.tmis.vehicle.common.service.VehicleApplyMaterialService;
import com.guanwei.tmis.vehicle.common.service.VehicleAttachmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 车辆申请材料服务实现
 *
 * <AUTHOR>
 * @date 2023/08/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VehicleApplyMaterialServiceImpl extends MBaseServiceImpl<VehicleApplyMaterialMapper, VehicleApplyMaterial>
    implements VehicleApplyMaterialService{

    private final VehicleApplyMaterialExMapStruct vehicleApplyMaterialExMapStruct;

    private final VehicleAttachmentService vehicleAttachmentService;

    private final VehicleAttachmentMapStruct vehicleAttachmentMapStruct;

    /**
     * 保存车辆申请材料
     *
     * @param vehicleApplyMaterialExDTOs 车辆申请材料拓展dto
     * @param applyId                    申请id
     */
    @Override
    public void saveVehicleApplyMaterials(List<VehicleApplyMaterialExDTO> vehicleApplyMaterialExDTOs, String applyId) {
        if (EmptyUtil.isNotEmpty(vehicleApplyMaterialExDTOs)) {
            List<Object> objects = this.listObjs(
                    Wrappers.lambdaQuery(VehicleApplyMaterial.class)
                            .select(VehicleApplyMaterial::getApplyMaterialId)
                            .eq(VehicleApplyMaterial::getApplyId, applyId));
            if (EmptyUtil.isNotEmpty(objects)) {
                //删除附件
                vehicleAttachmentService.remove(Wrappers.lambdaQuery(VehicleAttachment.class).in(VehicleAttachment::getApplyMaterialId, objects));
            }
            //移除材料清单
            this.remove(Wrappers.lambdaQuery(VehicleApplyMaterial.class).eq(VehicleApplyMaterial::getApplyId, applyId));
            for (VehicleApplyMaterialExDTO vehicleApplyMaterialExDTO : vehicleApplyMaterialExDTOs) {
                //材料清单
                VehicleApplyMaterial vehicleApplyMaterial = vehicleApplyMaterialExMapStruct.toSource(vehicleApplyMaterialExDTO);
                vehicleApplyMaterial.setApplyId(applyId);
                vehicleApplyMaterial.setCreateTime(new Date());
                this.save(vehicleApplyMaterial);
                //材料清单-对应的附件
                List<VehicleAttachmentDTO> vehicleAttachmentDTOs = vehicleApplyMaterialExDTO.getAttachs();
                if (EmptyUtil.isNotEmpty(vehicleAttachmentDTOs)) {
                    List<VehicleAttachment> list = new ArrayList<>();
                    for (VehicleAttachmentDTO vehicleAttachmentDTO : vehicleAttachmentDTOs) {
                        VehicleAttachment vehicleAttachment = vehicleAttachmentMapStruct.toSource(vehicleAttachmentDTO);
                        vehicleAttachment.setFileName(vehicleAttachmentDTO.getFileName());
                        vehicleAttachment.setApplyMaterialId(vehicleApplyMaterial.getApplyMaterialId());
                        list.add(vehicleAttachment);
                    }
                    vehicleAttachmentService.saveBatch(list);
                }
            }
        }
    }

    @Override
    public List<VehicleApplyMaterialExDTO> getVehicleApplyMaterials(String applyId) {
        List<VehicleApplyMaterialExDTO> materials = new ArrayList<>();

        // 查询材料清单
        List<VehicleApplyMaterial> vehicleApplyMaterials = this.getBaseMapper()
                .selectList(Wrappers.lambdaQuery(VehicleApplyMaterial.class).eq(VehicleApplyMaterial::getApplyId, applyId));

        // 根据材料清单查询对应的附件
        vehicleApplyMaterials.forEach(x -> {
            List<VehicleAttachment> vehicleAttachments = vehicleAttachmentService.getBaseMapper()
                    .selectList(Wrappers.lambdaQuery(VehicleAttachment.class).eq(VehicleAttachment::getApplyMaterialId, x.getApplyMaterialId()));
            // entity -> dto
            VehicleApplyMaterialExDTO vehicleApplyMaterialExDTO = vehicleApplyMaterialExMapStruct.toTarget(x);
            List<VehicleAttachmentDTO> vehicleAttachmentDTOS = vehicleAttachmentMapStruct.toTargetList(vehicleAttachments);
            TmisPhotoPathWriter tmisPhotoPathWriter = ApplicationContextHolder.getBean(TmisPhotoPathWriter.class);
            for (VehicleAttachmentDTO vehicleAttachmentDTO : vehicleAttachmentDTOS) {
                vehicleAttachmentDTO.setFileUrl(tmisPhotoPathWriter.write(vehicleAttachmentDTO.getFilePath()));
            }
            vehicleApplyMaterialExDTO.setAttachs(vehicleAttachmentDTOS);
            materials.add(vehicleApplyMaterialExDTO);
        });
        return materials;
    }
}




