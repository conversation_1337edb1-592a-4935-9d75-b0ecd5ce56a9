package com.guanwei.tmis.vehicle.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanwei.mybatis.base.service.MBaseServiceImpl;
import com.guanwei.tmis.vehicle.common.dto.VehicleApplyProcessDTO;
import com.guanwei.tmis.vehicle.common.dto.mapstruct.VehicleApplyProcessMapStruct;
import com.guanwei.tmis.vehicle.common.entity.VehicleApplyProcess;
import com.guanwei.tmis.vehicle.common.mapper.VehicleApplyProcessMapper;
import com.guanwei.tmis.vehicle.common.service.VehicleApplyProcessService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车辆申请办理过程服务实现
 *
 * <AUTHOR>
 * @date 2024/09/24
 */
@Service
@RequiredArgsConstructor
public class VehicleApplyProcessServiceImpl extends MBaseServiceImpl<VehicleApplyProcessMapper, VehicleApplyProcess> implements VehicleApplyProcessService {

    private final VehicleApplyProcessMapStruct vehicleApplyProcessMapStruct;

    @Override
    public List<VehicleApplyProcessDTO> getProcessList(String applyId) {
        LambdaQueryWrapper<VehicleApplyProcess> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VehicleApplyProcess::getApplyId, applyId);
        wrapper.orderByAsc(VehicleApplyProcess::getProcessTime);
        List<VehicleApplyProcess> list = baseMapper.selectList(wrapper);
        return list.stream().map(item -> {
            VehicleApplyProcessDTO dto = new VehicleApplyProcessDTO();
            vehicleApplyProcessMapStruct.updateToTarget(item, dto);
            return dto;
        }).toList();
    }
}
