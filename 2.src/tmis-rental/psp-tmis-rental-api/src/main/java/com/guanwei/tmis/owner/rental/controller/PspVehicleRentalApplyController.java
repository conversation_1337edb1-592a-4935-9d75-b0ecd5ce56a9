package com.guanwei.tmis.owner.rental.controller;

import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanwei.core.utils.result.R;
import com.guanwei.mybatis.base.controller.MBaseController;
import com.guanwei.tmis.common.enums.EnumOwnerOperState;
import com.guanwei.tmis.common.enums.EnumRentalRecordType;
import com.guanwei.tmis.common.psp.controller.PspBaseController;
import com.guanwei.tmis.common.psp.token.AccountContextHolder;
import com.guanwei.tmis.owner.base.entity.RoadOwner;
import com.guanwei.tmis.owner.base.service.RoadOwnerService;
import com.guanwei.tmis.owner.common.rental.dto.VehicleRentalApplyCommonDTO;
import com.guanwei.tmis.owner.common.rental.dto.VehicleRentalApplyInfoDTO;
import com.guanwei.tmis.owner.common.rental.query.VehicleApplyQuery;
import com.guanwei.tmis.owner.common.rental.query.VehicleRentalApplyQuery;
import com.guanwei.tmis.owner.common.rental.service.VehicleRentalApplyService;
import com.guanwei.tmis.owner.rental.service.PspVehicleRentalApplyWorkflowService;
import com.guanwei.tmis.vehicle.common.dto.VehicleApplyMaterialExDTO;
import com.guanwei.token.annotation.BootNonToken;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 企业端-城市客运-租赁车辆管理-备案管理-车辆办理记录
 *
 * <AUTHOR>
 * @date 2023/10/23
 */
@Slf4j
@RestController
@RequestMapping("/v1/vehicle-rental-apply")
@AllArgsConstructor
public class PspVehicleRentalApplyController extends PspBaseController {

    private final VehicleRentalApplyService vehicleRentalApplyService;

    private final RoadOwnerService roadOwnerService;

    private final PspVehicleRentalApplyWorkflowService pspVehicleRentalApplyWorkflowService;

    /**
     * 车辆受理信息查询
     *
     * @param query 查询条件
     * @return {@link R}<{@link ?}>
     */
    @GetMapping("/mobile/list")
    public R<?> list(@Validated VehicleRentalApplyQuery query) {
        // 安全校验-是否是本企业-通过业户id
        super.isCurrentOwnerAccountByOwnerId(query.getOwnerId());
        // 安全校验-是否是本企业-通过统一社会信用代码
        super.isCurrentOwnerAccountBySocialCreditCode(query.getSocialCreditCode());

        List<VehicleRentalApplyCommonDTO> list = vehicleRentalApplyService.getvehicleRentalApplyList(query);
        return R.OK(list);
    }

    /**
     * 车辆受理信息查询导出
     *
     * @param query 查询条件
     * @param response 请求
     */
    @BootNonToken
    @GetMapping("/mobile/export")
    public void export(VehicleRentalApplyQuery query, HttpServletResponse response){
        vehicleRentalApplyService.export(query,response);
    }

    /**
     * 移动端未受理列表
     *
     * @param query 查询
     * @return {@link R}<{@link List}<{@link VehicleRentalApplyInfoDTO}>>
     */
    @GetMapping("/mobile/apply/not-accept-list")
    public R<List<VehicleRentalApplyInfoDTO>> mobileNotAcceptList(VehicleApplyQuery query) {
        // 安全校验-是否是本企业-通过业户id
        super.isCurrentOwnerAccountByOwnerId(query.getOwnerId());

        String ownerId = AccountContextHolder.getAccountInfoContext().getOwnerId();
        String socialCreditCode = AccountContextHolder.getAccountInfoContext().getSocialCreditCode();
        RoadOwner roadOwner;
        // 无证业户登录的微信端
        // if (ownerId.equals("0") || EmptyUtil.isEmpty(ownerId)) {
        //     Assert.hasText(socialCreditCode, "当前登录账号统一社会信用代码为空！");
        //     roadOwner = roadOwnerService.getOne(new LambdaQueryWrapper<RoadOwner>().eq(RoadOwner::getSocialCreditCode, socialCreditCode), false);
        // } else {
        //     roadOwner = roadOwnerService.getById(ownerId);
        // }
        Assert.hasText(socialCreditCode, "当前登录账号统一社会信用代码为空！");
        LambdaQueryWrapper<RoadOwner> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RoadOwner::getSocialCreditCode, socialCreditCode);
        wrapper.eq(RoadOwner::getOperState, EnumOwnerOperState.Operating.getValue());
        roadOwner = roadOwnerService.getOne(wrapper, Boolean.FALSE);
        Assert.notNull(roadOwner, "未找到业户档案信息");
        query.setOwnerId(roadOwner.getOwnerId());
        query.setRecordType(EnumRentalRecordType.Normal.getValue());
        List<VehicleRentalApplyInfoDTO> list = vehicleRentalApplyService.getvehicleRentalApplyCheckList(query);
        return R.OK(list);
    }

    /**
     * 作废
     *
     * @param applyId                申请id
     * @return {@link R}<{@link String}>
     */
    @Lock4j(keys = "#applyId")
    @PostMapping("/{applyId}/invalid")
    public R<String> invalid(@PathVariable String applyId) {
        pspVehicleRentalApplyWorkflowService.invalid(applyId, AccountContextHolder.getAccountInfoContext());
        return R.OK("作废成功！");
    }


    /**
     * init材料
     *
     * @param orgId     机构ID
     * @param applyType 申请类型
     * @return {@link R }<{@link List }<{@link VehicleApplyMaterialExDTO }>>
     */
    @GetMapping("/init/materials/{orgId}/{applyType}")
    public R<List<VehicleApplyMaterialExDTO>> initMaterials(@PathVariable String orgId, @PathVariable Integer applyType) {
        List<VehicleApplyMaterialExDTO> materials = pspVehicleRentalApplyWorkflowService.initMaterials(orgId, applyType);
        return R.OK(materials);
    }
}
