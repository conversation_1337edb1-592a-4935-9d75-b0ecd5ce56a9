package com.guanwei.tmis.owner.rental.controller;

import cn.hutool.core.lang.Assert;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.core.utils.result.R;
import com.guanwei.mybatis.base.controller.MBaseController;
import com.guanwei.tmis.common.enums.EnumCertificateState;
import com.guanwei.tmis.common.psp.controller.PspBaseController;
import com.guanwei.tmis.common.psp.token.AccountContextHolder;
import com.guanwei.tmis.owner.common.rental.dto.PspVehicleRentalDTO;
import com.guanwei.tmis.owner.common.rental.query.PspVehicleRentalQuery;
import com.guanwei.tmis.owner.common.rental.service.VehicleRentalService;
import com.guanwei.token.annotation.BootNonToken;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 微信端-城市客运-租赁车辆管理-信息查询
 *
 * <AUTHOR>
 * @date 2023/10/16
 */
@Slf4j
@RestController
@RequestMapping("/v1/vehicle-rental")
@RequiredArgsConstructor
public class PspVehicleRentalController extends PspBaseController {

    private final VehicleRentalService vehicleRentalService;

    /**
     * 车辆档案查询
     *
     * @param query 查询条件
     * @return {@link R}<{@link List}<{@link PspVehicleRentalDTO}>>
     */
    @GetMapping("/mobile/list")
    public R<List<PspVehicleRentalDTO>> list(PspVehicleRentalQuery query) {
        // 安全校验-是否是本企业-通过业户id
        super.isCurrentOwnerAccountByOwnerId(query.getOwnerId());
        // 安全校验-是否是本企业-通过统一社会信用代码
        super.isCurrentOwnerAccountBySocialCreditCode(query.getSocialCreditCode());

        String ownerId = AccountContextHolder.getAccountInfoContext().getOwnerId();
        String socialCreditCode = AccountContextHolder.getAccountInfoContext().getSocialCreditCode();
        // 无证业户登录的微信端
        // if (ownerId.equals("0") || EmptyUtil.isEmpty(ownerId)) {
        //     Assert.notNull(socialCreditCode, "当前登录账号统一社会信用代码为空！");
        //     query.setSocialCreditCode(socialCreditCode);
        // } else {
        //     query.setOwnerId(ownerId);
        // }
        Assert.notNull(socialCreditCode, "当前登录账号统一社会信用代码为空！");
        query.setSocialCreditCode(socialCreditCode);
        List<PspVehicleRentalDTO> list = vehicleRentalService.getPspVehicleRentalList(query);
        return R.OK(list);
    }

    /**
     * 车辆档案查询导出
     *
     * @param query    查询条件
     * @param response 请求
     */
    @BootNonToken
    @GetMapping("/mobile/export")
    public void exportPspVehicleRentalList(PspVehicleRentalQuery query, HttpServletResponse response) {
        vehicleRentalService.exportPspVehicleRentalList(query, response);
    }

    /**
     * 根据车牌号码和车牌颜色查询车辆信息(给南京使用的，地区编码前端获取不到)
     *
     * @param query 查询条件
     * @return {@link R}<{@link PspVehicleRentalDTO}>
     */
    @BootNonToken
    @GetMapping("/mobile/vehicleInfo")
    public R<PspVehicleRentalDTO> getVehicleByVehicleNumAndPlateColor(PspVehicleRentalQuery query) {
        // 安全校验-是否是本企业-通过业户id
        super.isCurrentOwnerAccountByOwnerId(query.getOwnerId());
        // 安全校验-是否是本企业-通过统一社会信用代码
        super.isCurrentOwnerAccountBySocialCreditCode(query.getSocialCreditCode());

        query.setAreaCode("320100");
        query.setCertState(EnumCertificateState.Valid.getValue());
        PspVehicleRentalDTO vehicle = vehicleRentalService.getVehicleByVehicleNumAndPlateColor(query);
        return R.OK(vehicle);
    }

}
