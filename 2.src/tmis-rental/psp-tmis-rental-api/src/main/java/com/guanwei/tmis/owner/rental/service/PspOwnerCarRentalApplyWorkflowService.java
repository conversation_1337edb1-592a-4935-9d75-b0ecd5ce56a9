package com.guanwei.tmis.owner.rental.service;

import com.guanwei.tmis.common.psp.token.AccountInfo;
import com.guanwei.workflow.dto.WorkflowProcessDataDTO;

/**
 * 企业端工作流服务
 *
 * <AUTHOR>
 * @date 2023/9/14
 */
public interface PspOwnerCarRentalApplyWorkflowService {


    /**
     * 作废
     *
     * @param workflowProcessDataDTO 工作流过程数据dto
     * @param accountInfo            帐户信息
     * <AUTHOR>
     * @date 2023-09-14
     **/
    void invalid(WorkflowProcessDataDTO workflowProcessDataDTO, AccountInfo accountInfo);

    /**
     * 微信端-作废租赁申请 (关联备案) 只有备案才会有这种申请
     *
     * @param workflowProcessDataDTO 工作流过程数据dto
     * @param accountInfoContext     帐户信息上下文
     */
    void associationBatchInvalid(WorkflowProcessDataDTO workflowProcessDataDTO, AccountInfo accountInfoContext);
}
