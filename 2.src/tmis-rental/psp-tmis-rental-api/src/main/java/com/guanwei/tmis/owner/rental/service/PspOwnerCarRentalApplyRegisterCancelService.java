package com.guanwei.tmis.owner.rental.service;

import com.guanwei.tmis.common.psp.token.AccountInfo;
import com.guanwei.tmis.owner.common.rental.dto.CarRentalApplyCommonInfoDTO;


/**
 * 租赁业户备案变更
 *
 * <AUTHOR>
 * @date 2023/10/20
 */
public interface PspOwnerCarRentalApplyRegisterCancelService {


    /**
     * 提交
     *
     * @param carRentalApplyCommonInfoDTO 汽车出租申请通用信息dto
     * @param accountInfoContext          帐户信息上下文
     * @param value                       值
     */
    void submit(CarRentalApplyCommonInfoDTO carRentalApplyCommonInfoDTO, AccountInfo accountInfoContext, Integer value);

    /**
     * 微信端-租赁业户-备案注销
     *
     * @param carRentalApplyCommonInfoDTO 汽车出租申请通用信息dto
     * @param accountInfoContext          用户信息
     */
    void wxSubmit(CarRentalApplyCommonInfoDTO carRentalApplyCommonInfoDTO, AccountInfo accountInfoContext, Integer value);

    /**
     * 微信端-租赁业户-备案注销-暂存
     *
     * @param carRentalApplyCommonInfoDTO 汽车出租申请通用信息dto
     * @param accountInfoContext          用户信息
     */
    void temporary(CarRentalApplyCommonInfoDTO carRentalApplyCommonInfoDTO, AccountInfo accountInfoContext, Integer value);
}
