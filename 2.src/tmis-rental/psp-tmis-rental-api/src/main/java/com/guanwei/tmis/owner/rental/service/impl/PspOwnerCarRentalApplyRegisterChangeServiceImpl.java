package com.guanwei.tmis.owner.rental.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.tmis.common.dto.SPBusinessApplyRecipientDTO;
import com.guanwei.tmis.common.entity.SPBusinessApplyRecipient;
import com.guanwei.tmis.common.entity.SPCatalogCoding;
import com.guanwei.tmis.common.entity.SysOrganize;
import com.guanwei.tmis.common.enums.*;
import com.guanwei.tmis.common.mapstruct.SPBusinessApplyRecipientMapStruct;
import com.guanwei.tmis.common.psp.token.AccountInfo;
import com.guanwei.tmis.common.service.CodeRuleService;
import com.guanwei.tmis.common.service.SPBusinessApplyRecipientService;
import com.guanwei.tmis.common.service.SPCatalogCodingService;
import com.guanwei.tmis.common.service.SysOrganizeService;
import com.guanwei.tmis.owner.base.dto.*;
import com.guanwei.tmis.owner.base.dto.mapstruct.RoadOwnerApplyBaseMapStruct;
import com.guanwei.tmis.owner.base.dto.mapstruct.RoadOwnerApplyCertMapStruct;
import com.guanwei.tmis.owner.base.dto.mapstruct.RoadOwnerApplyMapStruct;
import com.guanwei.tmis.owner.base.dto.mapstruct.RoadOwnerApplyPersMapStruct;
import com.guanwei.tmis.owner.base.entity.RoadOwnerApply;
import com.guanwei.tmis.owner.base.entity.RoadOwnerApplyBase;
import com.guanwei.tmis.owner.base.entity.RoadOwnerApplyCert;
import com.guanwei.tmis.owner.base.entity.RoadOwnerApplyPers;
import com.guanwei.tmis.owner.base.event.OwnerApplyEvent;
import com.guanwei.tmis.owner.base.service.*;
import com.guanwei.tmis.owner.common.rental.dto.CarRentalApplyCommonInfoDTO;
import com.guanwei.tmis.owner.common.rental.dto.OwnerCarRentalApplyDTO;
import com.guanwei.tmis.owner.common.rental.dto.OwnerCarRentalApplyServiceOrgDTO;
import com.guanwei.tmis.owner.common.rental.entity.OwnerCarRentalApply;
import com.guanwei.tmis.owner.common.rental.mapstruct.OwnerCarRentalApplyCommonInfoMapStruct;
import com.guanwei.tmis.owner.common.rental.mapstruct.OwnerCarRentalApplyMapStruct;
import com.guanwei.tmis.owner.common.rental.service.OwnerCarRentalApplyCommonService;
import com.guanwei.tmis.owner.common.rental.service.OwnerCarRentalApplyService;
import com.guanwei.tmis.owner.common.rental.service.OwnerCarRentalApplyServiceOrgService;
import com.guanwei.tmis.owner.common.rental.service.impl.verify.OwnerCarRentalApplyVerify;
import com.guanwei.tmis.owner.common.road.service.OwnerCommonService;
import com.guanwei.tmis.owner.rental.service.PspOwnerCarRentalApplyRegisterChangeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

/**
 * 租赁业户备案变更
 *
 * <AUTHOR>
 * @date 2023-10-19  14:34
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PspOwnerCarRentalApplyRegisterChangeServiceImpl implements PspOwnerCarRentalApplyRegisterChangeService {

    private final SysOrganizeService sysOrganizeService;
    private final OwnerCarRentalApplyVerify ownerCarRentalApplyVerify;
    private final OwnerCarRentalApplyCommonInfoMapStruct ownerCarRentalApplyCommonInfoMapStruct;
    private final RoadOwnerApplyMapStruct roadOwnerApplyMapStruct;
    private final RoadOwnerApplyBaseMapStruct roadOwnerApplyBaseMapStruct;
    private final RoadOwnerApplyCertMapStruct roadOwnerApplyCertMapStruct;
    private final RoadOwnerApplyPersMapStruct roadOwnerApplyPersMapStruct;
    private final OwnerCarRentalApplyMapStruct ownerCarRentalApplyMapStruct;
    private final SPBusinessApplyRecipientMapStruct spBusinessApplyRecipientMapStruct;
    private final SPCatalogCodingService spCatalogCodingService;
    private final RoadOwnerApplyService roadOwnerApplyService;
    private final RoadOwnerApplyBaseService roadOwnerApplyBaseService;
    private final OwnerCommonService ownerCommonService;
    private final RoadOwnerApplyCertService roadOwnerApplyCertService;
    private final RoadOwnerApplyPersService roadOwnerApplyPersService;
    private final OwnerCarRentalApplyService ownerCarRentalApplyService;
    private final RoadOwnerApplyMaterialService roadOwnerApplyMaterialService;
    private final RoadOwnerApplyBusiScopeService roadOwnerApplyBusiScopeService;
    private final SPBusinessApplyRecipientService spBusinessApplyRecipientService;
    private final OwnerCarRentalApplyServiceOrgService ownerCarRentalApplyServiceOrgService;
    private final OwnerCarRentalApplyCommonService ownerCarRentalApplyCommonService;
    private final CodeRuleService codeRuleService;
    private final ApplicationContext applicationContext;

    /**
     * 提交实现接口
     *
     * @param carRentalApplyCommonInfoDTO 维护申请常见信息dto
     * @param accountInfo                 帐户信息
     * @param source                      来源
     * <AUTHOR>
     * @date 2023-10-19
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submit(CarRentalApplyCommonInfoDTO carRentalApplyCommonInfoDTO, AccountInfo accountInfo, Integer source) {
        log.info("租赁备案，申请基础信息：{}", carRentalApplyCommonInfoDTO.getBaseInfo());
        log.info("租赁备案，申请证件信息：{}", carRentalApplyCommonInfoDTO.getCertInfo());
        log.info("租赁备案，经办人信息：{}", carRentalApplyCommonInfoDTO.getOperatorInfo());
        log.info("租赁备案，负责人信息：{}", carRentalApplyCommonInfoDTO.getPrincipalInfo());
        log.info("租赁备案，法人信息：{}", carRentalApplyCommonInfoDTO.getLegalInfo());
        log.info("租赁备案，实际控制人信息：{}", carRentalApplyCommonInfoDTO.getControllerInfo());
        log.info("租赁备案，业务申办收件人信息：{}", carRentalApplyCommonInfoDTO.getRecipient());
        log.info("租赁备案，经营范围：{}", carRentalApplyCommonInfoDTO.getBusiScopes());
        log.info("租赁备案，附件清单：{}", carRentalApplyCommonInfoDTO.getMaterials());

        submitInfo(carRentalApplyCommonInfoDTO, accountInfo, source, EnumApplyState.Submit.getValue());

    }

    /**
     * 微信端-租赁业户-备案变更-暂存
     *
     * @param carRentalApplyCommonInfoDTO 汽车出租申请通用信息dto
     * @param accountInfo                 帐户信息
     * @param source                      来源
     * @param state                       申请状态
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void temporary(CarRentalApplyCommonInfoDTO carRentalApplyCommonInfoDTO, AccountInfo accountInfo, Integer source, Integer state) {
        log.info("微信端租赁备案-暂存，申请基础信息：{}", carRentalApplyCommonInfoDTO.getBaseInfo());
        log.info("微信端租赁备案-暂存，申请证件信息：{}", carRentalApplyCommonInfoDTO.getCertInfo());
        log.info("微信端租赁备案-暂存，经办人信息：{}", carRentalApplyCommonInfoDTO.getOperatorInfo());
        log.info("微信端租赁备案-暂存，负责人信息：{}", carRentalApplyCommonInfoDTO.getPrincipalInfo());
        log.info("微信端租赁备案-暂存，法人信息：{}", carRentalApplyCommonInfoDTO.getLegalInfo());
        log.info("微信端租赁备案-暂存，实际控制人信息：{}", carRentalApplyCommonInfoDTO.getControllerInfo());
        log.info("微信端租赁备案-暂存，业务申办收件人信息：{}", carRentalApplyCommonInfoDTO.getRecipient());
        log.info("微信端租赁备案-暂存，经营范围：{}", carRentalApplyCommonInfoDTO.getBusiScopes());
        log.info("微信端租赁备案-暂存，附件清单：{}", carRentalApplyCommonInfoDTO.getMaterials());

        submitInfo(carRentalApplyCommonInfoDTO, accountInfo, source, state);
    }

    /**
     * 微信端-租赁业户-备案变更-提交
     *
     * @param carRentalApplyCommonInfoDTO 汽车租赁将公共信息应用到
     * @param accountInfo                 帐户信息
     * @param source                      来源
     * @param state                       申请状态
     **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void vxSubmit(CarRentalApplyCommonInfoDTO carRentalApplyCommonInfoDTO, AccountInfo accountInfo, Integer source, Integer state) {
        log.info("微信端租赁备案-提交，申请基础信息：{}", carRentalApplyCommonInfoDTO.getBaseInfo());
        log.info("微信端租赁备案-提交，申请证件信息：{}", carRentalApplyCommonInfoDTO.getCertInfo());
        log.info("微信端租赁备案-提交，经办人信息：{}", carRentalApplyCommonInfoDTO.getOperatorInfo());
        log.info("微信端租赁备案-提交，负责人信息：{}", carRentalApplyCommonInfoDTO.getPrincipalInfo());
        log.info("微信端租赁备案-提交，法人信息：{}", carRentalApplyCommonInfoDTO.getLegalInfo());
        log.info("微信端租赁备案-提交，实际控制人信息：{}", carRentalApplyCommonInfoDTO.getControllerInfo());
        log.info("微信端租赁备案-提交，业务申办收件人信息：{}", carRentalApplyCommonInfoDTO.getRecipient());
        log.info("微信端租赁备案-提交，经营范围：{}", carRentalApplyCommonInfoDTO.getBusiScopes());
        log.info("微信端租赁备案-提交，附件清单：{}", carRentalApplyCommonInfoDTO.getMaterials());

        submitInfo(carRentalApplyCommonInfoDTO, accountInfo, source, state);
    }

    private void submitInfo(CarRentalApplyCommonInfoDTO carRentalApplyCommonInfoDTO, AccountInfo accountInfo, Integer source, Integer state) {
        RoadOwnerApplyPersDTO operatorInfo = carRentalApplyCommonInfoDTO.getOperatorInfo();
        RoadOwnerApplyPersDTO legalInfoDTO = carRentalApplyCommonInfoDTO.getLegalInfo();
        RoadOwnerApplyPersDTO principalInfoDTO = carRentalApplyCommonInfoDTO.getPrincipalInfo();

        List<RoadOwnerApplyBusiScopeDTO> roadOwnerBusiScopeDTOS = carRentalApplyCommonInfoDTO.getBusiScopes();
        ///Assert.isTrue(EmptyUtil.isNotEmpty(roadOwnerBusiScopeDTOS), "经营范围不可为空！");

        List<RoadOwnerApplyMaterialExDTO> roadOwnerApplyMaterialExDTOS = carRentalApplyCommonInfoDTO.getMaterials();

        //如果是退回的申请，这里重新生成一条申请
        boolean isBack = EmptyUtil.isNotEmpty(carRentalApplyCommonInfoDTO.getState()) &&
                carRentalApplyCommonInfoDTO.getState().equals(EnumApplyState.Return.getValue());
        if (isBack) {
            String applyId = IdUtil.getSnowflakeNextIdStr();
            carRentalApplyCommonInfoDTO.setApplyId(applyId);
            //全部赋值为空，统一走下面的逻辑赋值
            carRentalApplyCommonInfoDTO.setCreator("");
            carRentalApplyCommonInfoDTO.setBusinessNo("");
            carRentalApplyCommonInfoDTO.setComments("");
            carRentalApplyCommonInfoDTO.getBaseInfo().setCreateTime(null);
            carRentalApplyCommonInfoDTO.getCertInfo().setCreateTime(null);
            carRentalApplyCommonInfoDTO.getOwnerCarRentalApply().setCreateTime(null);
            carRentalApplyCommonInfoDTO.getOperatorInfo().setPersId("");
            carRentalApplyCommonInfoDTO.getLegalInfo().setPersId("");
            carRentalApplyCommonInfoDTO.getPrincipalInfo().setPersId("");
            if(EmptyUtil.isNotEmpty(roadOwnerBusiScopeDTOS)){
                roadOwnerBusiScopeDTOS.forEach(roadOwnerApplyBusiScopeDTO -> {
                    roadOwnerApplyBusiScopeDTO.setApplyBusiScopeId("");
                });
            }else {
                RoadOwnerApplyCertDTO certInfo = carRentalApplyCommonInfoDTO.getCertInfo();
                if(EmptyUtil.isNotEmpty(certInfo)){
                    String busiScopeCodes = certInfo.getBusiScopeCodes();
                    String busiScopeNames = certInfo.getBusiScopeNames();
                    RoadOwnerApplyBusiScopeDTO ownerApplyBusiScopeDTO = new RoadOwnerApplyBusiScopeDTO();
                    ownerApplyBusiScopeDTO.setBusiScopeCode(busiScopeCodes);
                    ownerApplyBusiScopeDTO.setBusiScopeName(busiScopeNames);
                    ownerApplyBusiScopeDTO.setApplyBusiScopeId("");
                    roadOwnerBusiScopeDTOS.add(ownerApplyBusiScopeDTO);
                }
            }

            carRentalApplyCommonInfoDTO.setBusiScopes(roadOwnerBusiScopeDTOS);
            if (EmptyUtil.isNotEmpty(roadOwnerApplyMaterialExDTOS)) {
                roadOwnerApplyMaterialExDTOS.forEach(roadOwnerApplyMaterialExDTO -> {
                            roadOwnerApplyMaterialExDTO.setApplyMaterialId("");
                            if (EmptyUtil.isNotEmpty(roadOwnerApplyMaterialExDTO.getAttachs())) {
                                List<RoadOwnerAttachDTO> roadOwnerAttachDTOS = roadOwnerApplyMaterialExDTO.getAttachs();
                                roadOwnerAttachDTOS.forEach(roadOwnerAttachDTO -> {
                                    roadOwnerAttachDTO.setAttachId("");
                                });
                                roadOwnerApplyMaterialExDTO.setAttachs(roadOwnerAttachDTOS);
                            }
                        }
                );
                carRentalApplyCommonInfoDTO.setMaterials(roadOwnerApplyMaterialExDTOS);
            }
        }
        List<OwnerCarRentalApplyServiceOrgDTO> ownerCarRentalApplyServiceOrgDTOs =
                carRentalApplyCommonInfoDTO.getOwnerCarRentalApplyServiceOrgs();
        Assert.notNull(carRentalApplyCommonInfoDTO.getBaseInfo().getCountyCode(), "区县编码不可为空！");
        Assert.isTrue(EmptyUtil.isNotEmpty(ownerCarRentalApplyServiceOrgDTOs), "经营所在地服务机构信息不能为空！");

        //先确定企业用户提交的申请该由谁来受理，找到管辖机构
        SysOrganize sysOrganize = null;
        if (EmptyUtil.isEmpty(carRentalApplyCommonInfoDTO.getOrgId())) {
            //先确定企业用户提交的申请该由谁来受理，找到管辖机构
            //根据用户选择的区县编码查找受理机构
            List<SysOrganize> sysOrganizes = sysOrganizeService.list(Wrappers.lambdaQuery(SysOrganize.class)
                    .eq(SysOrganize::getAreaCode, carRentalApplyCommonInfoDTO.getBaseInfo().getAreaCode())
                    .eq(SysOrganize::getCounty, carRentalApplyCommonInfoDTO.getBaseInfo().getCountyCode())
                    .notLike(SysOrganize::getOrgName, "作废"));
            if (sysOrganizes.size() == 0) {
                log.error("根据隶属区县查询管辖机构信息为空!{}", carRentalApplyCommonInfoDTO.getBaseInfo().getCountyCode());
            } else {
                if (sysOrganizes.size() > 1) {
                    log.error("根据隶属区县查询管辖机构信息有多条", sysOrganizes);
                }
                sysOrganize = sysOrganizes.get(0);
            }
            //根据隶属区县查询为空时,通过行政区划查询市级机构
            if (EmptyUtil.isEmpty(sysOrganize)) {
                sysOrganizes = sysOrganizeService.list(Wrappers.lambdaQuery(SysOrganize.class)
                        .eq(SysOrganize::getAreaCode, accountInfo.getAreaCode())
                        .eq(SysOrganize::getCounty, accountInfo.getAreaCode())
                        .notLike(SysOrganize::getOrgName, "作废"));
                Assert.isTrue(sysOrganizes.size() > 0, "根据当前用户信息查询管辖机构信息为空!");
                sysOrganize = sysOrganizes.get(0);
                log.info("用户提交的租赁业户备案业务，需要由【{}】来受理！", sysOrganize.getOrgName());
            }
        } else {
            sysOrganize = sysOrganizeService.getById(carRentalApplyCommonInfoDTO.getOrgId());
        }
        Assert.notNull(sysOrganize, "管辖机构不存在！");
        carRentalApplyCommonInfoDTO.setOrgId(sysOrganize.getOrgId());

        //业务验证
        ownerCarRentalApplyVerify.verify(carRentalApplyCommonInfoDTO);

        //1.归档保存业户申请表
        RoadOwnerApplyDTO roadOwnerApplyDTO = ownerCarRentalApplyCommonInfoMapStruct.toOwnerApplyDTO(carRentalApplyCommonInfoDTO);
        Assert.isTrue(EmptyUtil.isNotEmpty(accountInfo.getSocialCreditCode()) &&
                        EmptyUtil.isNotEmpty(roadOwnerApplyDTO.getSocialCreditCode()) &&
                        accountInfo.getSocialCreditCode().equals(roadOwnerApplyDTO.getSocialCreditCode()),
                "填报的统一社会信用代码与账号绑定不一致！");
        RoadOwnerApply roadOwnerApply = roadOwnerApplyMapStruct.toSource(roadOwnerApplyDTO);

        // 提交清空暂存中的申请记录
        if(state.equals(EnumApplyState.Submit.getValue())){
            String applyReason = roadOwnerApply.getApplyReason();
            if(EmptyUtil.isNotEmpty(applyReason)){
                String[] applyReasonArr = applyReason.split(";");
                roadOwnerApply.setApplyReason(applyReasonArr[0] + ";" + ownerCarRentalApplyCommonService.ownerCarRentalRecordChange(carRentalApplyCommonInfoDTO));
            }else {
                roadOwnerApply.setApplyReason(roadOwnerApply.getApplyReason() + ";" + ownerCarRentalApplyCommonService.ownerCarRentalRecordChange(carRentalApplyCommonInfoDTO));
            }
        }else {
            roadOwnerApply.setApplyReason(roadOwnerApply.getApplyReason() + ";" + ownerCarRentalApplyCommonService.ownerCarRentalRecordChange(carRentalApplyCommonInfoDTO));
        }
        roadOwnerApply.setState(state);
        roadOwnerApply.setDecisionResult(EnumDecisionResult.None.getValue());
        roadOwnerApply.setCheckState(EnumCheckState.None.getValue());
        //受理信息
        roadOwnerApply.setAcceptResult(EnumAcceptResult.None.getValue());

        //管辖信息
        roadOwnerApply.setOrgId(sysOrganize.getOrgId());
        roadOwnerApply.setOrgName(sysOrganize.getOrgName());

        //经办人
        roadOwnerApply.setOperator(operatorInfo.getPersName());
        roadOwnerApply.setTelephone(operatorInfo.getMobile());
        roadOwnerApply.setMobile(operatorInfo.getMobile());
        roadOwnerApply.setEmail(operatorInfo.getEmail());

        //因无法区分  暂存->提交    退回->暂存->提交，与维修微信端保持一致，这里每次提交时都更新为最新时间
        roadOwnerApply.setApplyDate(new Date());

        //基础信息
        if (EmptyUtil.isEmpty(roadOwnerApply.getCreator())) {
            roadOwnerApply.setCreator(accountInfo.getAccountName());
        }
        if (EmptyUtil.isEmpty(roadOwnerApply.getCreateTime())) {
            roadOwnerApply.setCreateTime(new Date());
        }
        roadOwnerApply.setModifier(accountInfo.getAccountName());
        roadOwnerApply.setModifyTime(new Date());
        roadOwnerApply.setAreaCode(accountInfo.getAreaCode());
        roadOwnerApply.setSource(source);

        if (EmptyUtil.isEmpty(roadOwnerApply.getBusinessNo())) {
            roadOwnerApply.setBusinessNo(ownerCommonService.getDocCaseNo(roadOwnerApply));
        }
        //查询经济类型名称
        List<SPCatalogCoding> codings =
                spCatalogCodingService.list(Wrappers.<SPCatalogCoding>lambdaQuery().eq(SPCatalogCoding::getCodingClass,
                        EnumConstCatalogCoding.ECONOMIC_TYPE_CODE.getValue()).eq(SPCatalogCoding::getCode,
                        roadOwnerApply.getEconomicType()));
        if (EmptyUtil.isNotEmpty(codings)) {
            roadOwnerApply.setEconTypeName(codings.get(0).getCodeName());
        }
        roadOwnerApplyService.saveOrUpdate(roadOwnerApply);

        //2.归档从业资格申请基础表
        RoadOwnerApplyBaseDTO roadOwnerApplyBaseDTO = carRentalApplyCommonInfoDTO.getBaseInfo();
        Assert.isTrue(EmptyUtil.isNotEmpty(accountInfo.getSocialCreditCode()) && EmptyUtil.isNotEmpty(roadOwnerApplyBaseDTO.getSocialCreditCode()) && accountInfo.getSocialCreditCode().equals(roadOwnerApplyBaseDTO.getSocialCreditCode()), "填报的统一社会信用代码与账号绑定不一致！");
        RoadOwnerApplyBase roadOwnerApplyBase = roadOwnerApplyBaseMapStruct.toSource(roadOwnerApplyBaseDTO);
        roadOwnerApplyBase.setSocialCreditCode(carRentalApplyCommonInfoDTO.getSocialCreditCode());
        roadOwnerApplyBase.setLegalName(legalInfoDTO.getPersName());
        roadOwnerApplyBase.setPrincipal(principalInfoDTO.getPersName());
        roadOwnerApplyBase.setPrincIdCardNum(principalInfoDTO.getIdCardNum());
        roadOwnerApplyBase.setApplyId(roadOwnerApply.getApplyId());
        roadOwnerApplyBase.setAddress(roadOwnerApplyBase.getAddress1() + roadOwnerApplyBase.getAddress2());
        if (EmptyUtil.isEmpty(roadOwnerApplyBase.getCreateTime())) {
            roadOwnerApplyBase.setCreateTime(new Date());
        }
        roadOwnerApplyBase.setModifyTime(new Date());
        roadOwnerApplyBase.setPostcode(carRentalApplyCommonInfoDTO.getPostcode());
        roadOwnerApplyBaseService.saveOrUpdate(roadOwnerApplyBase);

        //3.归档证件申请表
        RoadOwnerApplyCertDTO roadOwnerApplyCertDTO = carRentalApplyCommonInfoDTO.getCertInfo();
        RoadOwnerApplyCert roadOwnerApplyCert = roadOwnerApplyCertMapStruct.toSource(roadOwnerApplyCertDTO);
        roadOwnerApplyCert.setApplyId(roadOwnerApply.getApplyId());
        if (EmptyUtil.isEmpty(roadOwnerApplyCert.getCreateTime())) {
            roadOwnerApplyCert.setCreateTime(new Date());
        }
        roadOwnerApplyCert.setModifyTime(new Date());
        roadOwnerApplyCertService.saveOrUpdate(roadOwnerApplyCert);


        //4.归档经办人申请信息
        RoadOwnerApplyPers roadOwnerApplyPers = roadOwnerApplyPersMapStruct.toSource(operatorInfo);
        roadOwnerApplyPers.setApplyId(roadOwnerApply.getApplyId());
        roadOwnerApplyPers.setIdCertType(EnumIdCertificateType.IdCard.getValue());
        roadOwnerApplyPers.setModifier(accountInfo.getAccountName());
        roadOwnerApplyPers.setModifyTime(new Date());
        //roadOwnerApplyPers.setPersType(EnumOwnerPersType.Operator.getValue());
        roadOwnerApplyPersService.saveOrUpdate(roadOwnerApplyPers);

        //5.归档法人申请信息
        RoadOwnerApplyPers legalInfo = roadOwnerApplyPersMapStruct.toSource(legalInfoDTO);
        legalInfo.setApplyId(roadOwnerApply.getApplyId());
        legalInfo.setModifier(accountInfo.getAccountName());
        legalInfo.setPersType(EnumOwnerPersType.Legal.getValue());
        legalInfo.setModifyTime(new Date());
        roadOwnerApplyPersService.saveOrUpdate(legalInfo);

        //6.归档负责人申请信息
        RoadOwnerApplyPers principalInfo = roadOwnerApplyPersMapStruct.toSource(principalInfoDTO);
        principalInfo.setApplyId(roadOwnerApply.getApplyId());
        principalInfo.setModifier(accountInfo.getAccountName());
        principalInfo.setModifyTime(new Date());
        principalInfo.setPersType(EnumOwnerPersType.Principal.getValue());
        roadOwnerApplyPersService.saveOrUpdate(principalInfo);

        //7归档租赁业户申请信息
        OwnerCarRentalApplyDTO ownerMaintenanceApplyDTO = carRentalApplyCommonInfoDTO.getOwnerCarRentalApply();
        OwnerCarRentalApply ownerCarRentalApply = ownerCarRentalApplyMapStruct.toSource(ownerMaintenanceApplyDTO);
        ownerCarRentalApply.setApplyId(roadOwnerApply.getApplyId());
        if (EmptyUtil.isEmpty(ownerCarRentalApply.getCreateTime())) {
            ownerCarRentalApply.setCreateTime(new Date());
        }
        ownerCarRentalApply.setModifyTime(new Date());
        ownerCarRentalApplyService.saveOrUpdate(ownerCarRentalApply);

        //8汽车租赁业户申请所在地服务机构，areacode = countycode
        ownerCarRentalApplyServiceOrgService.saveOwnerCarRentalApplyServiceOrgs(ownerCarRentalApplyServiceOrgDTOs,
                roadOwnerApply.getApplyId(), roadOwnerApplyBaseDTO.getCountyCode());


        //9.归档申请材料信息
        roadOwnerApplyMaterialService.saveOwnerApplyMaterial(roadOwnerApplyMaterialExDTOS, roadOwnerApply.getApplyId());

        //10.归档经营范围申请信息
        roadOwnerApplyBusiScopeService.saveRoadOwnerApplyBusiScopes(roadOwnerBusiScopeDTOS, roadOwnerApply.getApplyId());

        //11.归档申请人邮寄信息
        if (carRentalApplyCommonInfoDTO.getIsMail().equals(EnumYesOrNot.Yes.getValue())) {
            //归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = carRentalApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(roadOwnerApply.getApplyId());
            spBusinessApplyRecipientService.saveOrUpdate(spBusinessApplyRecipient);
        }

        //12.生成案号
        if (EmptyUtil.isEmpty(roadOwnerApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(sysOrganize.getOrgId(), CodeRuleService.CASE_NO_CAR_RENTAL, true);
            roadOwnerApply.setBusinessNo(docCaseNo);
            roadOwnerApplyService.update(roadOwnerApply);
        }

        //10.发布事件
        applicationContext.publishEvent(new OwnerApplyEvent(roadOwnerApply.getApplyId(), EnumOwnerApplyEvent.Created));
    }


}
