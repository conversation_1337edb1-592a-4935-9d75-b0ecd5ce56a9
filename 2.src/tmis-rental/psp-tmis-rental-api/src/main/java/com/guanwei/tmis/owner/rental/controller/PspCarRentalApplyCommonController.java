package com.guanwei.tmis.owner.rental.controller;

import com.guanwei.core.utils.result.R;
import com.guanwei.mybatis.base.controller.MBaseController;
import com.guanwei.tmis.owner.base.dto.RoApplyInitDTO;
import com.guanwei.tmis.owner.common.rental.dto.CarRentalApplyCommonInfoDTO;
import com.guanwei.tmis.owner.common.rental.service.OwnerCarRentalApplyCommonService;
import com.guanwei.tmis.owner.common.rental.service.OwnerCarRentalApplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小微型客车租赁-通用申请接口
 *
 * <AUTHOR>
 * @date 2022/11/01
 */
@Slf4j
@RestController
@RequestMapping("/v1/apply")
@RequiredArgsConstructor
public class PspCarRentalApplyCommonController extends MBaseController {

    private final OwnerCarRentalApplyCommonService ownerCarRentalApplyCommonService;

    private final OwnerCarRentalApplyService ownerCarRentalApplyService;

    /**
     * 汽车租赁申请-初始化数据
     *
     * @param roApplyInitDTO 罗依申请初始化dto
     * @return {@link R}<{@link CarRentalApplyCommonInfoDTO}>
     */
    @PostMapping("/init")
    public R<CarRentalApplyCommonInfoDTO> init(@Validated @RequestBody RoApplyInitDTO roApplyInitDTO) {
        return R.OK(ownerCarRentalApplyCommonService.init(roApplyInitDTO));
    }

    /**
     * 汽车租赁申请-获取指定的申请信息
     *
     * @param id id
     * @return {@link R}<{@link CarRentalApplyCommonInfoDTO}>
     */
    @GetMapping("/{id}")
    public R<CarRentalApplyCommonInfoDTO> getCarRentalApplyCommonInfoDTO(@PathVariable String id) {
        return R.OK(ownerCarRentalApplyService.getCarRentalApplyCommonInfoDTO(id));
    }

    /**
     * 查询关联的车辆申请
     *
     * @param id 业户id
     * @return {@link List }<{@link String }>
     */
    @GetMapping("/association/{id}")
    public R<List<String>> getAssociationApply(@PathVariable String id) {
        List<String> vehicleApplyList = ownerCarRentalApplyService.getAssociationApply(id);
        return R.OK(vehicleApplyList);
    }
}
