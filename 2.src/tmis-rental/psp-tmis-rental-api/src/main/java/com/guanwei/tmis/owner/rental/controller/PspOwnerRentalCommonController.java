package com.guanwei.tmis.owner.rental.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanwei.core.utils.result.R;
import com.guanwei.tmis.common.psp.controller.PspBaseController;
import com.guanwei.tmis.owner.common.rental.entity.OwnerCarRentalServiceOrg;
import com.guanwei.tmis.owner.common.rental.service.OwnerCarRentalServiceOrgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 租赁业户查询
 *
 * <AUTHOR>
 * @date 2023/12/15
 */
@Slf4j
@RestController
@RequestMapping("/v1/owner/rental")
@RequiredArgsConstructor
public class PspOwnerRentalCommonController extends PspBaseController {

    private final OwnerCarRentalServiceOrgService ownerCarRentalServiceOrgService;

    /**
     * 根据业户id查询所在地服务机构
     *
     * @param ownerId
     * @return
     */
    @GetMapping("/{ownerId}")
    public R<List<OwnerCarRentalServiceOrg>> getOwnerCarRentalServiceOrgByOwnerId(@PathVariable String ownerId){
        // 安全校验-是否是本企业-通过业户id
        super.isCurrentOwnerAccountByOwnerId(ownerId);

        LambdaQueryWrapper<OwnerCarRentalServiceOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OwnerCarRentalServiceOrg::getOwnerId,ownerId);
        List<OwnerCarRentalServiceOrg> list = ownerCarRentalServiceOrgService.list(queryWrapper);
        return R.OK(list);
    }

}
