package com.guanwei.tmis.owner.rental.controller;

import com.baomidou.lock.annotation.Lock4j;
import com.guanwei.core.utils.result.R;
import com.guanwei.tmis.common.psp.token.AccountContextHolder;
import com.guanwei.tmis.owner.common.rental.dto.VehicleApplyCommonInfoDTO;
import com.guanwei.tmis.owner.rental.service.PspVehicleRentalApplyRegisterCancelService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 租赁车辆管理-备案注销
 *
 * <AUTHOR>
 * @date 2023/10/19
 */
@Slf4j
@RestController
@RequestMapping("/v1/vehicle/register-cancel")
@RequiredArgsConstructor
public class PspVehicleRentalApplyRegisterCancelController {

    private final PspVehicleRentalApplyRegisterCancelService pspVehicleRentalApplyRegisterCancelService;

    /**
     * 车辆租赁-备案注销-暂存
     *
     * @param vehicleApplyCommonInfoDTO 车辆租赁应用通用信息
     * @param applyId                   申请标识ID
     * @return {@link R}<{@link ?}>
     */
    @Lock4j(keys = "#applyId")
    @PostMapping("/apply/{applyId}/temporary")
    public R<?> temporary(@PathVariable String applyId, @Validated @RequestBody VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO) {
        Assert.isTrue(applyId.equals(vehicleApplyCommonInfoDTO.getApplyId()), "申请ID不一致");
        pspVehicleRentalApplyRegisterCancelService.temporary(vehicleApplyCommonInfoDTO, AccountContextHolder.getAccountInfoContext());
        return R.OK();
    }


    /**
     * 车辆租赁-备案注销-登记提交
     *
     * @param vehicleApplyCommonInfoDTO 车辆租赁应用通用信息
     * @param applyId                   申请标识ID
     * @return {@link R}<{@link ?}>
     */
    @Lock4j(keys = "#applyId")
    @PostMapping("/apply/{applyId}/submit")
    public R<?> submit(@PathVariable String applyId, @Validated @RequestBody VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO) {
        Assert.isTrue(applyId.equals(vehicleApplyCommonInfoDTO.getApplyId()), "申请ID不一致");
        pspVehicleRentalApplyRegisterCancelService.submit(vehicleApplyCommonInfoDTO, AccountContextHolder.getAccountInfoContext());
        return R.OK();
    }

    /**
     * 微信端-车辆租赁-备案-登记提交
     *
     * @param vehicleApplyCommonInfoDTO 车辆租赁应用通用信息
     * @param applyId                   申请标识ID
     * @return {@link R}<{@link ?}>
     */
    @Lock4j(keys = "#applyId")
    @PostMapping("/apply/{applyId}/wx-submit")
    public R<?> wxSubmit(@PathVariable String applyId, @Validated @RequestBody VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO) {
        Assert.isTrue(applyId.equals(vehicleApplyCommonInfoDTO.getApplyId()), "申请ID不一致");
        pspVehicleRentalApplyRegisterCancelService.wxSubmit(vehicleApplyCommonInfoDTO, AccountContextHolder.getAccountInfoContext());
        return R.OK();
    }

    /**
     * 车辆租赁-备案注销预审-暂存
     *
     * @param vehicleApplyCommonInfoDTO 车辆租赁应用通用信息
     * @param applyId                   申请标识ID
     * @return {@link R}<{@link ?}>
     */
    @Lock4j(keys = "#applyId")
    @PostMapping("/apply/{applyId}/preview/temporary")
    public R<?> previewTemporary(@PathVariable String applyId, @Validated @RequestBody VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO) {
        Assert.isTrue(applyId.equals(vehicleApplyCommonInfoDTO.getApplyId()), "申请ID不一致");
        pspVehicleRentalApplyRegisterCancelService.previewTemporary(vehicleApplyCommonInfoDTO, AccountContextHolder.getAccountInfoContext());
        return R.OK();
    }

    /**
     * 车辆租赁-备案注销预审-提交
     *
     * @param vehicleApplyCommonInfoDTO 车辆租赁应用通用信息
     * @param applyId                   申请标识ID
     * @return {@link R}<{@link ?}>
     */
    @Lock4j(keys = "#applyId")
    @PostMapping("/apply/{applyId}/preview/submit")
    public R<?> previewSubmit(@PathVariable String applyId, @Validated @RequestBody VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO) {
        Assert.isTrue(applyId.equals(vehicleApplyCommonInfoDTO.getApplyId()), "申请ID不一致");
        pspVehicleRentalApplyRegisterCancelService.previewSubmit(vehicleApplyCommonInfoDTO, AccountContextHolder.getAccountInfoContext());
        return R.OK();
    }

    /**
     * 车辆租赁-备案注销预审-提交
     *
     * @param vehicleApplyCommonInfoDTO 车辆租赁应用通用信息
     * @param applyId                   申请标识ID
     * @return {@link R}<{@link ?}>
     */
    @Lock4j(keys = "#applyId")
    @PostMapping("/apply/{applyId}/preview/wx-submit")
    public R<?> previewWxSubmit(@PathVariable String applyId, @Validated @RequestBody VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO) {
        Assert.isTrue(applyId.equals(vehicleApplyCommonInfoDTO.getApplyId()), "申请ID不一致");
        pspVehicleRentalApplyRegisterCancelService.previewWxSubmit(vehicleApplyCommonInfoDTO, AccountContextHolder.getAccountInfoContext());
        return R.OK();
    }

    /**
     * 租赁车辆注销预审
     *
     * @param applyId                   申请id
     * @param vehicleApplyCommonInfoDTO 车辆租赁应用通用信息
     * @param response                  响应
     **/
    @Lock4j(keys = "#applyId")
    @PostMapping("/doc/{applyId}")
    public R<String> downloadFiles(@PathVariable String applyId, @RequestBody VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, HttpServletResponse response) {
        Assert.isTrue(applyId.equals(vehicleApplyCommonInfoDTO.getApplyId()), "申请ID不一致");
        String path = pspVehicleRentalApplyRegisterCancelService.downloadFiles(vehicleApplyCommonInfoDTO, response);
        return R.OK(path);
    }
}
