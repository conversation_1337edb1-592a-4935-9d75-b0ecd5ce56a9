package com.guanwei.tmis.owner.rental.service;

import com.guanwei.tmis.common.psp.token.AccountInfo;
import com.guanwei.tmis.vehicle.common.dto.VehicleApplyMaterialExDTO;

import java.util.List;

/**
 * psp车辆租赁申请工作流服务
 *
 * <AUTHOR>
 * @date 2023/10/23
 */
public interface PspVehicleRentalApplyWorkflowService {

    /**
     * 作废
     *
     * @param applyId  申请id
     * @param accountInfo 用户信息
     */
    void invalid(String applyId, AccountInfo accountInfo);

    /**
     * init材料
     *
     * @param orgId     机构ID
     * @param applyType 申请类型
     * @return {@link List }<{@link VehicleApplyMaterialExDTO }>
     */
    List<VehicleApplyMaterialExDTO> initMaterials(String orgId, Integer applyType);
}
