package com.guanwei.tmis.owner.rental.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepoove.poi.XWPFTemplate;
import com.guanwei.core.ApplicationContextHolder;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.s3.AmazonS3Properties;
import com.guanwei.tmis.common.config.TmisPhotoPathWriter;
import com.guanwei.tmis.common.dto.SPBusinessApplyRecipientDTO;
import com.guanwei.tmis.common.entity.SPBusinessApplyRecipient;
import com.guanwei.tmis.common.entity.SysOrganize;
import com.guanwei.tmis.common.enums.*;
import com.guanwei.tmis.common.mapstruct.SPBusinessApplyRecipientMapStruct;
import com.guanwei.tmis.common.psp.token.AccountInfo;
import com.guanwei.tmis.common.service.CodeRuleService;
import com.guanwei.tmis.common.service.SPBusinessApplyRecipientService;
import com.guanwei.tmis.common.service.SysOrganizeService;
import com.guanwei.tmis.owner.base.entity.RoadOwner;
import com.guanwei.tmis.owner.base.entity.RoadOwnerPers;
import com.guanwei.tmis.owner.base.service.RoadOwnerPersService;
import com.guanwei.tmis.owner.base.service.RoadOwnerService;
import com.guanwei.tmis.owner.common.rental.dto.VehicleApplyCommonInfoDTO;
import com.guanwei.tmis.owner.common.rental.dto.VehicleRentalApplyDTO;
import com.guanwei.tmis.owner.common.rental.entity.OwnerCarRental;
import com.guanwei.tmis.owner.common.rental.entity.VehicleRentalApply;
import com.guanwei.tmis.owner.common.rental.mapstruct.VehicleApplyCommonInfoMapStruct;
import com.guanwei.tmis.owner.common.rental.mapstruct.VehicleRentalApplyMapStruct;
import com.guanwei.tmis.owner.common.rental.service.OwnerCarRentalService;
import com.guanwei.tmis.owner.common.rental.service.VehicleRentalApplyService;
import com.guanwei.tmis.owner.common.rental.service.impl.verify.VehicleRentalApplyVerify;
import com.guanwei.tmis.owner.rental.service.PspVehicleRentalApplyRegisterCancelService;
import com.guanwei.tmis.vehicle.common.dto.*;
import com.guanwei.tmis.vehicle.common.dto.mapstruct.VehicleApplyBaseMapStruct;
import com.guanwei.tmis.vehicle.common.dto.mapstruct.VehicleApplyExtMapStruct;
import com.guanwei.tmis.vehicle.common.dto.mapstruct.VehicleApplyMapStruct;
import com.guanwei.tmis.vehicle.common.entity.VehicleApply;
import com.guanwei.tmis.vehicle.common.entity.VehicleApplyBase;
import com.guanwei.tmis.vehicle.common.entity.VehicleApplyExt;
import com.guanwei.tmis.vehicle.common.event.VehicleApplyEvent;
import com.guanwei.tmis.vehicle.common.service.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;


/**
 * psp车辆租赁申请注册取消服务实现
 *
 * <AUTHOR>
 * @date 2023/10/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PspVehicleRentalApplyRegisterCancelServiceImpl implements PspVehicleRentalApplyRegisterCancelService {

    private final SysOrganizeService sysOrganizeService;
    private final VehicleApplyCommonInfoMapStruct vehicleApplyCommonInfoMapStruct;
    private final VehicleApplyMapStruct vehicleApplyMapStruct;
    private final VehicleApplyService vehicleApplyService;
    private final VehicleApplyBaseMapStruct vehicleApplyBaseMapStruct;
    private final VehicleApplyBaseService vehicleApplyBaseService;
    private final VehicleApplyExtMapStruct vehicleApplyExtMapStruct;
    private final VehicleApplyExtService vehicleApplyExtService;
    private final VehicleRentalApplyMapStruct vehicleRentalApplyMapStruct;
    private final VehicleRentalApplyService vehicleRentalApplyService;
    private final VehicleApplyMaterialService vehicleApplyMaterialService;
    private final SPBusinessApplyRecipientMapStruct spBusinessApplyRecipientMapStruct;
    private final SPBusinessApplyRecipientService spBusinessApplyRecipientService;
    private final VehicleApplyPhotoService vehicleApplyPhotoService;
    private final VehicleApplyBusiScopeService vehicleApplyBusiScopeService;
    private final VehicleRentalApplyVerify vehicleRentalApplyVerify;
    private final CodeRuleService codeRuleService;
    private final RoadOwnerService roadOwnerService;
    private final OwnerCarRentalService ownerCarRentalService;
    private final RoadOwnerPersService roadOwnerPersService;
    private final ApplicationContext applicationContext;

    private final AmazonS3 amazonS3;

    private final AmazonS3Properties amazonS3Properties;

    private static final String PREFIX_PATH = "rental/tpl/";

    private static final String VEHICLE_RECORD_QUALIFIED_TPL_NAME = "VehicleRecordQualified.docx";

    private static final String FINAL_ORGID = "00000000000000000000000000000001";

    /**
     * 微信端-暂存
     *
     * @param vehicleApplyCommonInfoDTO 车辆出租申请通用信息dto
     * @param accountInfo                     帐户信息上下文
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void temporary(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfo) {

        log.info("车辆租赁备案暂存，申请租赁车辆信息：{}", vehicleApplyCommonInfoDTO.getVehicleApplyRental());
        log.info("车辆租赁备案暂存，申请基础信息：{}", vehicleApplyCommonInfoDTO.getBaseInfo());
        log.info("车辆租赁备案暂存，申请扩展信息：{}", vehicleApplyCommonInfoDTO.getExtInfo());
        log.info("车辆租赁备案暂存，附件清单：{}", vehicleApplyCommonInfoDTO.getMaterials());
        log.info("车辆租赁备案暂存，业务申办收件人信息：{}", vehicleApplyCommonInfoDTO.getRecipient());
        log.info("车辆租赁备案暂存，业户信息：{}", vehicleApplyCommonInfoDTO.getOwnerInfo());
        log.info("车辆租赁备案暂存，照片信息：{}", vehicleApplyCommonInfoDTO.getPhotos());
        log.info("车辆租赁备案暂存，车辆经营范围信息：{}", vehicleApplyCommonInfoDTO.getBusiScopes());

        vehicleRentalApplyVerify.verify(vehicleApplyCommonInfoDTO);

        VehicleRentalApplyDTO vehicleRentalApplyDTO = vehicleApplyCommonInfoDTO.getVehicleApplyRental();
        VehicleApplyBaseDTO vehicleApplyBaseDTO = vehicleApplyCommonInfoDTO.getBaseInfo();
        VehicleApplyExtDTO vehicleApplyExtDTO = vehicleApplyCommonInfoDTO.getExtInfo();
        List<VehicleApplyMaterialExDTO> vehicleApplyMaterialExDTOs = vehicleApplyCommonInfoDTO.getMaterials();
        List<VehicleApplyPhotoDTO> photos = vehicleApplyCommonInfoDTO.getPhotos();
        List<VehicleApplyBusiScopeDTO> busiScopes = vehicleApplyCommonInfoDTO.getBusiScopes();

        // Assert.isTrue(validate(vehicleApplyCommonInfoDTO), "必填字段验证不通过");
        SysOrganize sysOrganize = sysOrganizeService.getById(vehicleApplyCommonInfoDTO.getOrgId());
        Assert.notNull(sysOrganize, "id为[" + vehicleApplyCommonInfoDTO.getOrgId() + "]的管辖机构不可为空！");

        // ### 1.归档车辆申请表
        VehicleApplyDTO vehicleApplyDTO = vehicleApplyCommonInfoMapStruct.toVehicleApplyDTO(vehicleApplyCommonInfoDTO);
        VehicleApply vehicleApply = vehicleApplyMapStruct.toSource(vehicleApplyDTO);
        vehicleApply.setState(EnumApplyState.Editing.getValue());
        vehicleApply.setDecisionResult(EnumDecisionResult.None.getValue());
        vehicleApply.setCheckState(EnumCheckState.None.getValue());

        // 受理信息
        vehicleApply.setAcceptResult(EnumAcceptResult.None.getValue());
        vehicleApply.setAccepter(accountInfo.getAccountName());
        vehicleApply.setAcceptDate(new Date());
        vehicleApply.setAcceptOrgId(sysOrganize.getOrgId());
        vehicleApply.setAcceptOrgName(sysOrganize.getOrgName());

        // 管辖信息
        vehicleApply.setOrgId(sysOrganize.getOrgId());
        vehicleApply.setOrgName(sysOrganize.getOrgName());

        // 基础信息
        if (EmptyUtil.isEmpty(vehicleApply.getCreator())) {
            vehicleApply.setCreator(accountInfo.getAccountName());
        }
        vehicleApply.setCreateTime(new Date());
        vehicleApply.setModifier(accountInfo.getAccountName());
        vehicleApply.setModifyTime(new Date());
        vehicleApply.setAreaCode(accountInfo.getAreaCode());
        vehicleApply.setSource(EnumApplySource.Mobile.getValue());

        // vehicleApply.setOperator(accountInfo.getAccountName());

        vehicleApplyService.saveOrUpdate(vehicleApply);

        // ### 2.归档车辆申请基础表
        VehicleApplyBase vehicleApplyBase = vehicleApplyBaseMapStruct.toSource(vehicleApplyBaseDTO);
        vehicleApplyBase.setApplyId(vehicleApply.getApplyId());
        vehicleApplyBase.setCreateTime(new Date());
        vehicleApplyBase.setModifyTime(new Date());

        vehicleApplyBaseService.saveOrUpdate(vehicleApplyBase);

        // ### 3.归档车辆申请扩展表
        VehicleApplyExt vehicleApplyExt = vehicleApplyExtMapStruct.toSource(vehicleApplyExtDTO);
        vehicleApplyExt.setApplyId(vehicleApply.getApplyId());
        vehicleApplyExt.setCreateTime(new Date());
        vehicleApplyExt.setModifyTime(new Date());

        vehicleApplyExtService.saveOrUpdate(vehicleApplyExt);

        // ### 4.归档租赁车辆申请表
        VehicleRentalApply vehicleRentalApply = vehicleRentalApplyMapStruct.toSource(vehicleRentalApplyDTO);
        vehicleRentalApply.setApplyId(vehicleApply.getApplyId());
        vehicleRentalApply.setCreateTime(new Date());
        vehicleRentalApply.setModifyTime(new Date());

        vehicleRentalApplyService.saveOrUpdate(vehicleRentalApply);

        // ### 5.归档车辆申请材料信息
        vehicleApplyMaterialService.saveVehicleApplyMaterials(vehicleApplyMaterialExDTOs, vehicleApply.getApplyId());

        // ### 6.归档申请人邮寄信息
        if (Objects.equals(vehicleApplyCommonInfoDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            // 归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = vehicleApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(vehicleApply.getApplyId());
            spBusinessApplyRecipientService.saveOrUpdate(spBusinessApplyRecipient);
        }

        // ### 7.归档车辆照片信息
        vehicleApplyPhotoService.saveVehicleApplyPhoto(photos, vehicleApply.getApplyId());

        // ### 8.归档车辆申请经营范围
        vehicleApplyBusiScopeService.saveVehicleApplyBusiScope(busiScopes, vehicleApply.getApplyId());

        // ### 9.生成案号
        if (EmptyUtil.isEmpty(vehicleApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(sysOrganize.getOrgId(), CodeRuleService.CASE_NO_CAR_RENTAL, true);
            vehicleApply.setBusinessNo(docCaseNo);
            vehicleApplyService.update(vehicleApply);
        }

        //10.发布事件
        applicationContext.publishEvent(new VehicleApplyEvent(vehicleApply.getApplyId(), EnumVehicleApplyEvent.Created));
    }

    /**
     * 企业端-提交
     *
     * @param vehicleApplyCommonInfoDTO 车辆出租申请通用信息dto
     * @param accountInfo                     帐户信息上下文
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submit(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfo) {

        log.info("车辆租赁备案提交，申请租赁车辆信息：{}", vehicleApplyCommonInfoDTO.getVehicleApplyRental());
        log.info("车辆租赁备案提交，申请基础信息：{}", vehicleApplyCommonInfoDTO.getBaseInfo());
        log.info("车辆租赁备案提交，申请扩展信息：{}", vehicleApplyCommonInfoDTO.getExtInfo());
        log.info("车辆租赁备案提交，附件清单：{}", vehicleApplyCommonInfoDTO.getMaterials());
        log.info("车辆租赁备案提交，业务申办收件人信息：{}", vehicleApplyCommonInfoDTO.getRecipient());
        log.info("车辆租赁备案提交，业户信息：{}", vehicleApplyCommonInfoDTO.getOwnerInfo());
        log.info("车辆租赁备案提交，照片信息：{}", vehicleApplyCommonInfoDTO.getPhotos());
        log.info("车辆租赁备案提交，车辆经营范围信息：{}", vehicleApplyCommonInfoDTO.getBusiScopes());

        vehicleRentalApplyVerify.verify(vehicleApplyCommonInfoDTO);

        VehicleRentalApplyDTO vehicleRentalApplyDTO = vehicleApplyCommonInfoDTO.getVehicleApplyRental();
        VehicleApplyBaseDTO vehicleApplyBaseDTO = vehicleApplyCommonInfoDTO.getBaseInfo();
        VehicleApplyExtDTO vehicleApplyExtDTO = vehicleApplyCommonInfoDTO.getExtInfo();
        List<VehicleApplyMaterialExDTO> vehicleApplyMaterialExDTOs = vehicleApplyCommonInfoDTO.getMaterials();
        List<VehicleApplyPhotoDTO> photos = vehicleApplyCommonInfoDTO.getPhotos();
        List<VehicleApplyBusiScopeDTO> busiScopes = vehicleApplyCommonInfoDTO.getBusiScopes();

        // Assert.isTrue(validate(vehicleApplyCommonInfoDTO), "必填字段验证不通过");
        SysOrganize sysOrganize = sysOrganizeService.getById(vehicleApplyCommonInfoDTO.getOrgId());
        Assert.notNull(sysOrganize, "id为[" + vehicleApplyCommonInfoDTO.getOrgId() + "]的管辖机构不可为空！");

        // ### 1.归档车辆申请表
        VehicleApplyDTO vehicleApplyDTO = vehicleApplyCommonInfoMapStruct.toVehicleApplyDTO(vehicleApplyCommonInfoDTO);
        VehicleApply vehicleApply = vehicleApplyMapStruct.toSource(vehicleApplyDTO);
        vehicleApply.setState(EnumApplyState.Submit.getValue());
        vehicleApply.setDecisionResult(EnumDecisionResult.None.getValue());
        vehicleApply.setCheckState(EnumCheckState.None.getValue());

        // 受理信息
        vehicleApply.setAcceptResult(EnumAcceptResult.None.getValue());
        vehicleApply.setAccepter(accountInfo.getAccountName());
        vehicleApply.setAcceptDate(new Date());
        vehicleApply.setAcceptOrgId(sysOrganize.getOrgId());
        vehicleApply.setAcceptOrgName(sysOrganize.getOrgName());

        // 管辖信息
        vehicleApply.setOrgId(sysOrganize.getOrgId());
        vehicleApply.setOrgName(sysOrganize.getOrgName());

        // 基础信息
        if (EmptyUtil.isEmpty(vehicleApply.getCreator())) {
            vehicleApply.setCreator(accountInfo.getAccountName());
        }
        vehicleApply.setCreateTime(new Date());
        vehicleApply.setModifier(accountInfo.getAccountName());
        vehicleApply.setModifyTime(new Date());
        vehicleApply.setAreaCode(accountInfo.getAreaCode());
        vehicleApply.setSource(EnumApplySource.OnlineApply.getValue());

        vehicleApplyService.saveOrUpdate(vehicleApply);

        // ### 2.归档车辆申请基础表
        VehicleApplyBase vehicleApplyBase = vehicleApplyBaseMapStruct.toSource(vehicleApplyBaseDTO);
        vehicleApplyBase.setApplyId(vehicleApply.getApplyId());
        vehicleApplyBase.setCreateTime(new Date());
        vehicleApplyBase.setModifyTime(new Date());

        vehicleApplyBaseService.saveOrUpdate(vehicleApplyBase);

        // ### 3.归档车辆申请扩展表
        VehicleApplyExt vehicleApplyExt = vehicleApplyExtMapStruct.toSource(vehicleApplyExtDTO);
        vehicleApplyExt.setApplyId(vehicleApply.getApplyId());
        vehicleApplyExt.setCreateTime(new Date());
        vehicleApplyExt.setModifyTime(new Date());

        vehicleApplyExtService.saveOrUpdate(vehicleApplyExt);

        // ### 4.归档租赁车辆申请表
        VehicleRentalApply vehicleRentalApply = vehicleRentalApplyMapStruct.toSource(vehicleRentalApplyDTO);
        vehicleRentalApply.setApplyId(vehicleApply.getApplyId());
        vehicleRentalApply.setCreateTime(new Date());
        vehicleRentalApply.setModifyTime(new Date());
        if(EmptyUtil.isNotEmpty(accountInfo.getAreaCode()) && accountInfo.getAreaCode().equals("320100")){
            Integer materialPreviewType = vehicleRentalApply.getMaterialPreviewType();
            if (EmptyUtil.isNotEmpty(materialPreviewType) && materialPreviewType.equals(EnumRentalMaterialPreviewType.PreviewPass.getValue())) {
                vehicleRentalApply.setMaterialPreviewType(EnumRentalMaterialPreviewType.PreviewPassed.getValue());
            }
        }

        vehicleRentalApplyService.saveOrUpdate(vehicleRentalApply);

        // ### 5.归档车辆申请材料信息
        vehicleApplyMaterialService.saveVehicleApplyMaterials(vehicleApplyMaterialExDTOs, vehicleApply.getApplyId());

        // ### 6.归档申请人邮寄信息
        if (Objects.equals(vehicleApplyCommonInfoDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            // 归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = vehicleApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(vehicleApply.getApplyId());
            spBusinessApplyRecipientService.saveOrUpdate(spBusinessApplyRecipient);
        }

        // ### 7.归档车辆照片信息
        vehicleApplyPhotoService.saveVehicleApplyPhoto(photos, vehicleApply.getApplyId());

        // ### 8.归档车辆申请经营范围
        vehicleApplyBusiScopeService.saveVehicleApplyBusiScope(busiScopes, vehicleApply.getApplyId());

        // ### 9.生成案号
        if (EmptyUtil.isEmpty(vehicleApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(sysOrganize.getOrgId(), CodeRuleService.CASE_NO_CAR_RENTAL, true);
            vehicleApply.setBusinessNo(docCaseNo);
            vehicleApplyService.update(vehicleApply);
        }

        //10.发布事件
        applicationContext.publishEvent(new VehicleApplyEvent(vehicleApply.getApplyId(), EnumVehicleApplyEvent.Created));
    }

    /**
     * 微信端-提交
     *
     * @param vehicleApplyCommonInfoDTO 车辆出租申请通用信息dto
     * @param accountInfo                     帐户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void wxSubmit(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfo) {

        log.info("车辆租赁备案提交，申请租赁车辆信息：{}", vehicleApplyCommonInfoDTO.getVehicleApplyRental());
        log.info("车辆租赁备案提交，申请基础信息：{}", vehicleApplyCommonInfoDTO.getBaseInfo());
        log.info("车辆租赁备案提交，申请扩展信息：{}", vehicleApplyCommonInfoDTO.getExtInfo());
        log.info("车辆租赁备案提交，附件清单：{}", vehicleApplyCommonInfoDTO.getMaterials());
        log.info("车辆租赁备案提交，业务申办收件人信息：{}", vehicleApplyCommonInfoDTO.getRecipient());
        log.info("车辆租赁备案提交，业户信息：{}", vehicleApplyCommonInfoDTO.getOwnerInfo());
        log.info("车辆租赁备案提交，照片信息：{}", vehicleApplyCommonInfoDTO.getPhotos());
        log.info("车辆租赁备案提交，车辆经营范围信息：{}", vehicleApplyCommonInfoDTO.getBusiScopes());

        vehicleRentalApplyVerify.verify(vehicleApplyCommonInfoDTO);

        VehicleRentalApplyDTO vehicleRentalApplyDTO = vehicleApplyCommonInfoDTO.getVehicleApplyRental();
        VehicleApplyBaseDTO vehicleApplyBaseDTO = vehicleApplyCommonInfoDTO.getBaseInfo();
        VehicleApplyExtDTO vehicleApplyExtDTO = vehicleApplyCommonInfoDTO.getExtInfo();
        List<VehicleApplyMaterialExDTO> vehicleApplyMaterialExDTOs = vehicleApplyCommonInfoDTO.getMaterials();
        List<VehicleApplyPhotoDTO> photos = vehicleApplyCommonInfoDTO.getPhotos();
        List<VehicleApplyBusiScopeDTO> busiScopes = vehicleApplyCommonInfoDTO.getBusiScopes();

        // Assert.isTrue(validate(vehicleApplyCommonInfoDTO), "必填字段验证不通过");
        SysOrganize sysOrganize = sysOrganizeService.getById(vehicleApplyCommonInfoDTO.getOrgId());
        Assert.notNull(sysOrganize, "id为[" + vehicleApplyCommonInfoDTO.getOrgId() + "]的管辖机构不可为空！");

        // ### 1.归档车辆申请表
        VehicleApplyDTO vehicleApplyDTO = vehicleApplyCommonInfoMapStruct.toVehicleApplyDTO(vehicleApplyCommonInfoDTO);
        VehicleApply vehicleApply = vehicleApplyMapStruct.toSource(vehicleApplyDTO);
        vehicleApply.setState(EnumApplyState.Submit.getValue());
        vehicleApply.setDecisionResult(EnumDecisionResult.None.getValue());
        vehicleApply.setCheckState(EnumCheckState.None.getValue());

        // 受理信息
        vehicleApply.setAcceptResult(EnumAcceptResult.None.getValue());
        vehicleApply.setAccepter(accountInfo.getAccountName());
        vehicleApply.setAcceptDate(new Date());
        vehicleApply.setAcceptOrgId(sysOrganize.getOrgId());
        vehicleApply.setAcceptOrgName(sysOrganize.getOrgName());

        // 管辖信息
        vehicleApply.setOrgId(sysOrganize.getOrgId());
        vehicleApply.setOrgName(sysOrganize.getOrgName());

        // 基础信息
        if (EmptyUtil.isEmpty(vehicleApply.getCreator())) {
            vehicleApply.setCreator(accountInfo.getAccountName());
        }
        vehicleApply.setCreateTime(new Date());
        vehicleApply.setModifier(accountInfo.getAccountName());
        vehicleApply.setModifyTime(new Date());
        vehicleApply.setAreaCode(accountInfo.getAreaCode());
        vehicleApply.setSource(EnumApplySource.Mobile.getValue());

        // vehicleApply.setOperator(accountInfo.getAccountName());

        vehicleApplyService.saveOrUpdate(vehicleApply);

        // ### 2.归档车辆申请基础表
        VehicleApplyBase vehicleApplyBase = vehicleApplyBaseMapStruct.toSource(vehicleApplyBaseDTO);
        vehicleApplyBase.setApplyId(vehicleApply.getApplyId());
        vehicleApplyBase.setCreateTime(new Date());
        vehicleApplyBase.setModifyTime(new Date());

        vehicleApplyBaseService.saveOrUpdate(vehicleApplyBase);

        // ### 3.归档车辆申请扩展表
        VehicleApplyExt vehicleApplyExt = vehicleApplyExtMapStruct.toSource(vehicleApplyExtDTO);
        vehicleApplyExt.setApplyId(vehicleApply.getApplyId());
        vehicleApplyExt.setCreateTime(new Date());
        vehicleApplyExt.setModifyTime(new Date());

        vehicleApplyExtService.saveOrUpdate(vehicleApplyExt);

        // ### 4.归档租赁车辆申请表
        VehicleRentalApply vehicleRentalApply = vehicleRentalApplyMapStruct.toSource(vehicleRentalApplyDTO);
        vehicleRentalApply.setApplyId(vehicleApply.getApplyId());
        vehicleRentalApply.setCreateTime(new Date());
        vehicleRentalApply.setModifyTime(new Date());
        if(EmptyUtil.isNotEmpty(accountInfo.getAreaCode()) && accountInfo.getAreaCode().equals("320100")){
            Integer materialPreviewType = vehicleRentalApply.getMaterialPreviewType();
            if (EmptyUtil.isNotEmpty(materialPreviewType) && materialPreviewType.equals(EnumRentalMaterialPreviewType.PreviewPass.getValue())) {
                vehicleRentalApply.setMaterialPreviewType(EnumRentalMaterialPreviewType.PreviewPassed.getValue());
            }
        }
        vehicleRentalApplyService.saveOrUpdate(vehicleRentalApply);

        // ### 5.归档车辆申请材料信息
        vehicleApplyMaterialService.saveVehicleApplyMaterials(vehicleApplyMaterialExDTOs, vehicleApply.getApplyId());

        // ### 6.归档申请人邮寄信息
        if (Objects.equals(vehicleApplyCommonInfoDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            // 归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = vehicleApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(vehicleApply.getApplyId());
            spBusinessApplyRecipientService.saveOrUpdate(spBusinessApplyRecipient);
        }

        // ### 7.归档车辆照片信息
        vehicleApplyPhotoService.saveVehicleApplyPhoto(photos, vehicleApply.getApplyId());

        // ### 8.归档车辆申请经营范围
        vehicleApplyBusiScopeService.saveVehicleApplyBusiScope(busiScopes, vehicleApply.getApplyId());

        // ### 9.生成案号
        if (EmptyUtil.isEmpty(vehicleApply.getBusinessNo())) {
            String docCaseNo = codeRuleService.generateOrgNumber(sysOrganize.getOrgId(), CodeRuleService.CASE_NO_CAR_RENTAL, true);
            vehicleApply.setBusinessNo(docCaseNo);
            vehicleApplyService.update(vehicleApply);
        }

        //10.发布事件
        applicationContext.publishEvent(new VehicleApplyEvent(vehicleApply.getApplyId(), EnumVehicleApplyEvent.Created));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void previewTemporary(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfo) {
        log.info("车辆租赁备案暂存，申请租赁车辆信息：{}", vehicleApplyCommonInfoDTO.getVehicleApplyRental());
        log.info("车辆租赁备案暂存，申请基础信息：{}", vehicleApplyCommonInfoDTO.getBaseInfo());
        log.info("车辆租赁备案暂存，申请扩展信息：{}", vehicleApplyCommonInfoDTO.getExtInfo());
        log.info("车辆租赁备案暂存，附件清单：{}", vehicleApplyCommonInfoDTO.getMaterials());
        log.info("车辆租赁备案暂存，业务申办收件人信息：{}", vehicleApplyCommonInfoDTO.getRecipient());
        log.info("车辆租赁备案暂存，业户信息：{}", vehicleApplyCommonInfoDTO.getOwnerInfo());
        log.info("车辆租赁备案暂存，照片信息：{}", vehicleApplyCommonInfoDTO.getPhotos());
        log.info("车辆租赁备案暂存，车辆经营范围信息：{}", vehicleApplyCommonInfoDTO.getBusiScopes());

        vehicleRentalApplyVerify.verify(vehicleApplyCommonInfoDTO);

        VehicleRentalApplyDTO vehicleRentalApplyDTO = vehicleApplyCommonInfoDTO.getVehicleApplyRental();
        VehicleApplyBaseDTO vehicleApplyBaseDTO = vehicleApplyCommonInfoDTO.getBaseInfo();
        VehicleApplyExtDTO vehicleApplyExtDTO = vehicleApplyCommonInfoDTO.getExtInfo();
        List<VehicleApplyMaterialExDTO> vehicleApplyMaterialExDTOs = vehicleApplyCommonInfoDTO.getMaterials();
        List<VehicleApplyPhotoDTO> photos = vehicleApplyCommonInfoDTO.getPhotos();
        List<VehicleApplyBusiScopeDTO> busiScopes = vehicleApplyCommonInfoDTO.getBusiScopes();

        // Assert.isTrue(validate(vehicleApplyCommonInfoDTO), "必填字段验证不通过");
        SysOrganize sysOrganize = sysOrganizeService.getById(vehicleApplyCommonInfoDTO.getOrgId());
        Assert.notNull(sysOrganize, "id为[" + vehicleApplyCommonInfoDTO.getOrgId() + "]的管辖机构不可为空！");

        // ### 1.归档车辆申请表
        VehicleApplyDTO vehicleApplyDTO = vehicleApplyCommonInfoMapStruct.toVehicleApplyDTO(vehicleApplyCommonInfoDTO);
        VehicleApply vehicleApply = vehicleApplyMapStruct.toSource(vehicleApplyDTO);
        vehicleApply.setState(EnumApplyState.Editing.getValue());
        vehicleApply.setDecisionResult(EnumDecisionResult.None.getValue());
        vehicleApply.setCheckState(EnumCheckState.None.getValue());

        // 受理信息
        vehicleApply.setAcceptResult(EnumAcceptResult.None.getValue());
        vehicleApply.setAccepter(accountInfo.getAccountName());
        vehicleApply.setAcceptDate(new Date());
        vehicleApply.setAcceptOrgId(sysOrganize.getOrgId());
        vehicleApply.setAcceptOrgName(sysOrganize.getOrgName());

        // 管辖信息
        vehicleApply.setOrgId(sysOrganize.getOrgId());
        vehicleApply.setOrgName(sysOrganize.getOrgName());

        // 基础信息
        if (EmptyUtil.isEmpty(vehicleApply.getCreator())) {
            vehicleApply.setCreator(accountInfo.getAccountName());
        }
        vehicleApply.setCreateTime(new Date());
        vehicleApply.setModifier(accountInfo.getAccountName());
        vehicleApply.setModifyTime(new Date());
        vehicleApply.setAreaCode(accountInfo.getAreaCode());
        vehicleApply.setSource(EnumApplySource.Mobile.getValue());

        // vehicleApply.setOperator(accountInfo.getAccountName());

        vehicleApplyService.saveOrUpdate(vehicleApply);

        // ### 2.归档车辆申请基础表
        VehicleApplyBase vehicleApplyBase = vehicleApplyBaseMapStruct.toSource(vehicleApplyBaseDTO);
        vehicleApplyBase.setApplyId(vehicleApply.getApplyId());
        vehicleApplyBase.setCreateTime(new Date());
        vehicleApplyBase.setModifyTime(new Date());

        vehicleApplyBaseService.saveOrUpdate(vehicleApplyBase);

        // ### 3.归档车辆申请扩展表
        VehicleApplyExt vehicleApplyExt = vehicleApplyExtMapStruct.toSource(vehicleApplyExtDTO);
        vehicleApplyExt.setApplyId(vehicleApply.getApplyId());
        vehicleApplyExt.setCreateTime(new Date());
        vehicleApplyExt.setModifyTime(new Date());

        vehicleApplyExtService.saveOrUpdate(vehicleApplyExt);

        // ### 4.归档租赁车辆申请表
        VehicleRentalApply vehicleRentalApply = vehicleRentalApplyMapStruct.toSource(vehicleRentalApplyDTO);
        vehicleRentalApply.setApplyId(vehicleApply.getApplyId());
        vehicleRentalApply.setCreateTime(new Date());
        vehicleRentalApply.setModifyTime(new Date());
        vehicleRentalApply.setMaterialPreviewType(EnumRentalMaterialPreviewType.Editing.getValue());

        vehicleRentalApplyService.saveOrUpdate(vehicleRentalApply);

        // ### 5.归档车辆申请材料信息
        vehicleApplyMaterialService.saveVehicleApplyMaterials(vehicleApplyMaterialExDTOs, vehicleApply.getApplyId());

        // ### 6.归档申请人邮寄信息
        if (Objects.equals(vehicleApplyCommonInfoDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            // 归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = vehicleApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(vehicleApply.getApplyId());
            spBusinessApplyRecipientService.saveOrUpdate(spBusinessApplyRecipient);
        }

        // ### 7.归档车辆照片信息
        vehicleApplyPhotoService.saveVehicleApplyPhoto(photos, vehicleApply.getApplyId());

        // ### 8.归档车辆申请经营范围
        vehicleApplyBusiScopeService.saveVehicleApplyBusiScope(busiScopes, vehicleApply.getApplyId());

        //10.发布事件
        applicationContext.publishEvent(new VehicleApplyEvent(vehicleApply.getApplyId(), EnumVehicleApplyEvent.Created));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void previewSubmit(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfo) {
        log.info("车辆租赁备案暂存，申请租赁车辆信息：{}", vehicleApplyCommonInfoDTO.getVehicleApplyRental());
        log.info("车辆租赁备案暂存，申请基础信息：{}", vehicleApplyCommonInfoDTO.getBaseInfo());
        log.info("车辆租赁备案暂存，申请扩展信息：{}", vehicleApplyCommonInfoDTO.getExtInfo());
        log.info("车辆租赁备案暂存，附件清单：{}", vehicleApplyCommonInfoDTO.getMaterials());
        log.info("车辆租赁备案暂存，业务申办收件人信息：{}", vehicleApplyCommonInfoDTO.getRecipient());
        log.info("车辆租赁备案暂存，业户信息：{}", vehicleApplyCommonInfoDTO.getOwnerInfo());
        log.info("车辆租赁备案暂存，照片信息：{}", vehicleApplyCommonInfoDTO.getPhotos());
        log.info("车辆租赁备案暂存，车辆经营范围信息：{}", vehicleApplyCommonInfoDTO.getBusiScopes());

        vehicleRentalApplyVerify.verify(vehicleApplyCommonInfoDTO);

        VehicleRentalApplyDTO vehicleRentalApplyDTO = vehicleApplyCommonInfoDTO.getVehicleApplyRental();
        VehicleApplyBaseDTO vehicleApplyBaseDTO = vehicleApplyCommonInfoDTO.getBaseInfo();
        VehicleApplyExtDTO vehicleApplyExtDTO = vehicleApplyCommonInfoDTO.getExtInfo();
        List<VehicleApplyMaterialExDTO> vehicleApplyMaterialExDTOs = vehicleApplyCommonInfoDTO.getMaterials();
        List<VehicleApplyPhotoDTO> photos = vehicleApplyCommonInfoDTO.getPhotos();
        List<VehicleApplyBusiScopeDTO> busiScopes = vehicleApplyCommonInfoDTO.getBusiScopes();

        // Assert.isTrue(validate(vehicleApplyCommonInfoDTO), "必填字段验证不通过");
        SysOrganize sysOrganize = sysOrganizeService.getById(vehicleApplyCommonInfoDTO.getOrgId());
        Assert.notNull(sysOrganize, "id为[" + vehicleApplyCommonInfoDTO.getOrgId() + "]的管辖机构不可为空！");

        // ### 1.归档车辆申请表
        VehicleApplyDTO vehicleApplyDTO = vehicleApplyCommonInfoMapStruct.toVehicleApplyDTO(vehicleApplyCommonInfoDTO);
        VehicleApply vehicleApply = vehicleApplyMapStruct.toSource(vehicleApplyDTO);
        vehicleApply.setState(EnumApplyState.Submit.getValue());
        vehicleApply.setDecisionResult(EnumDecisionResult.None.getValue());
        vehicleApply.setCheckState(EnumCheckState.None.getValue());

        // 受理信息
        vehicleApply.setAcceptResult(EnumAcceptResult.None.getValue());
        vehicleApply.setAccepter(accountInfo.getAccountName());
        vehicleApply.setAcceptDate(new Date());
        vehicleApply.setAcceptOrgId(sysOrganize.getOrgId());
        vehicleApply.setAcceptOrgName(sysOrganize.getOrgName());

        // 管辖信息
        vehicleApply.setOrgId(sysOrganize.getOrgId());
        vehicleApply.setOrgName(sysOrganize.getOrgName());

        // 基础信息
        if (EmptyUtil.isEmpty(vehicleApply.getCreator())) {
            vehicleApply.setCreator(accountInfo.getAccountName());
        }
        vehicleApply.setCreateTime(new Date());
        vehicleApply.setModifier(accountInfo.getAccountName());
        vehicleApply.setModifyTime(new Date());
        vehicleApply.setAreaCode(accountInfo.getAreaCode());
        vehicleApply.setSource(EnumApplySource.OnlineApply.getValue());

        // vehicleApply.setOperator(accountInfo.getAccountName());

        vehicleApplyService.saveOrUpdate(vehicleApply);

        // ### 2.归档车辆申请基础表
        VehicleApplyBase vehicleApplyBase = vehicleApplyBaseMapStruct.toSource(vehicleApplyBaseDTO);
        vehicleApplyBase.setApplyId(vehicleApply.getApplyId());
        vehicleApplyBase.setCreateTime(new Date());
        vehicleApplyBase.setModifyTime(new Date());

        vehicleApplyBaseService.saveOrUpdate(vehicleApplyBase);

        // ### 3.归档车辆申请扩展表
        VehicleApplyExt vehicleApplyExt = vehicleApplyExtMapStruct.toSource(vehicleApplyExtDTO);
        vehicleApplyExt.setApplyId(vehicleApply.getApplyId());
        vehicleApplyExt.setCreateTime(new Date());
        vehicleApplyExt.setModifyTime(new Date());

        vehicleApplyExtService.saveOrUpdate(vehicleApplyExt);

        // ### 4.归档租赁车辆申请表
        VehicleRentalApply vehicleRentalApply = vehicleRentalApplyMapStruct.toSource(vehicleRentalApplyDTO);
        vehicleRentalApply.setApplyId(vehicleApply.getApplyId());
        vehicleRentalApply.setCreateTime(new Date());
        vehicleRentalApply.setModifyTime(new Date());
        vehicleRentalApply.setMaterialPreviewType(EnumRentalMaterialPreviewType.ToBePreview.getValue());

        vehicleRentalApplyService.saveOrUpdate(vehicleRentalApply);

        // ### 5.归档车辆申请材料信息
        vehicleApplyMaterialService.saveVehicleApplyMaterials(vehicleApplyMaterialExDTOs, vehicleApply.getApplyId());

        // ### 6.归档申请人邮寄信息
        if (Objects.equals(vehicleApplyCommonInfoDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            // 归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = vehicleApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(vehicleApply.getApplyId());
            spBusinessApplyRecipientService.saveOrUpdate(spBusinessApplyRecipient);
        }

        // ### 7.归档车辆照片信息
        vehicleApplyPhotoService.saveVehicleApplyPhoto(photos, vehicleApply.getApplyId());

        // ### 8.归档车辆申请经营范围
        vehicleApplyBusiScopeService.saveVehicleApplyBusiScope(busiScopes, vehicleApply.getApplyId());

        //10.发布事件
        applicationContext.publishEvent(new VehicleApplyEvent(vehicleApply.getApplyId(), EnumVehicleApplyEvent.Created));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void previewWxSubmit(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfo) {
        log.info("车辆租赁备案暂存，申请租赁车辆信息：{}", vehicleApplyCommonInfoDTO.getVehicleApplyRental());
        log.info("车辆租赁备案暂存，申请基础信息：{}", vehicleApplyCommonInfoDTO.getBaseInfo());
        log.info("车辆租赁备案暂存，申请扩展信息：{}", vehicleApplyCommonInfoDTO.getExtInfo());
        log.info("车辆租赁备案暂存，附件清单：{}", vehicleApplyCommonInfoDTO.getMaterials());
        log.info("车辆租赁备案暂存，业务申办收件人信息：{}", vehicleApplyCommonInfoDTO.getRecipient());
        log.info("车辆租赁备案暂存，业户信息：{}", vehicleApplyCommonInfoDTO.getOwnerInfo());
        log.info("车辆租赁备案暂存，照片信息：{}", vehicleApplyCommonInfoDTO.getPhotos());
        log.info("车辆租赁备案暂存，车辆经营范围信息：{}", vehicleApplyCommonInfoDTO.getBusiScopes());

        vehicleRentalApplyVerify.verify(vehicleApplyCommonInfoDTO);

        VehicleRentalApplyDTO vehicleRentalApplyDTO = vehicleApplyCommonInfoDTO.getVehicleApplyRental();
        VehicleApplyBaseDTO vehicleApplyBaseDTO = vehicleApplyCommonInfoDTO.getBaseInfo();
        VehicleApplyExtDTO vehicleApplyExtDTO = vehicleApplyCommonInfoDTO.getExtInfo();
        List<VehicleApplyMaterialExDTO> vehicleApplyMaterialExDTOs = vehicleApplyCommonInfoDTO.getMaterials();
        List<VehicleApplyPhotoDTO> photos = vehicleApplyCommonInfoDTO.getPhotos();
        List<VehicleApplyBusiScopeDTO> busiScopes = vehicleApplyCommonInfoDTO.getBusiScopes();

        // Assert.isTrue(validate(vehicleApplyCommonInfoDTO), "必填字段验证不通过");
        SysOrganize sysOrganize = sysOrganizeService.getById(vehicleApplyCommonInfoDTO.getOrgId());
        Assert.notNull(sysOrganize, "id为[" + vehicleApplyCommonInfoDTO.getOrgId() + "]的管辖机构不可为空！");

        // ### 1.归档车辆申请表
        VehicleApplyDTO vehicleApplyDTO = vehicleApplyCommonInfoMapStruct.toVehicleApplyDTO(vehicleApplyCommonInfoDTO);
        VehicleApply vehicleApply = vehicleApplyMapStruct.toSource(vehicleApplyDTO);
        vehicleApply.setState(EnumApplyState.Submit.getValue());
        vehicleApply.setDecisionResult(EnumDecisionResult.None.getValue());
        vehicleApply.setCheckState(EnumCheckState.None.getValue());

        // 受理信息
        vehicleApply.setAcceptResult(EnumAcceptResult.None.getValue());
        vehicleApply.setAccepter(accountInfo.getAccountName());
        vehicleApply.setAcceptDate(new Date());
        vehicleApply.setAcceptOrgId(sysOrganize.getOrgId());
        vehicleApply.setAcceptOrgName(sysOrganize.getOrgName());

        // 管辖信息
        vehicleApply.setOrgId(sysOrganize.getOrgId());
        vehicleApply.setOrgName(sysOrganize.getOrgName());

        // 基础信息
        if (EmptyUtil.isEmpty(vehicleApply.getCreator())) {
            vehicleApply.setCreator(accountInfo.getAccountName());
        }
        vehicleApply.setCreateTime(new Date());
        vehicleApply.setModifier(accountInfo.getAccountName());
        vehicleApply.setModifyTime(new Date());
        vehicleApply.setAreaCode(accountInfo.getAreaCode());
        vehicleApply.setSource(EnumApplySource.Mobile.getValue());

        // vehicleApply.setOperator(accountInfo.getAccountName());

        vehicleApplyService.saveOrUpdate(vehicleApply);

        // ### 2.归档车辆申请基础表
        VehicleApplyBase vehicleApplyBase = vehicleApplyBaseMapStruct.toSource(vehicleApplyBaseDTO);
        vehicleApplyBase.setApplyId(vehicleApply.getApplyId());
        vehicleApplyBase.setCreateTime(new Date());
        vehicleApplyBase.setModifyTime(new Date());

        vehicleApplyBaseService.saveOrUpdate(vehicleApplyBase);

        // ### 3.归档车辆申请扩展表
        VehicleApplyExt vehicleApplyExt = vehicleApplyExtMapStruct.toSource(vehicleApplyExtDTO);
        vehicleApplyExt.setApplyId(vehicleApply.getApplyId());
        vehicleApplyExt.setCreateTime(new Date());
        vehicleApplyExt.setModifyTime(new Date());

        vehicleApplyExtService.saveOrUpdate(vehicleApplyExt);

        // ### 4.归档租赁车辆申请表
        VehicleRentalApply vehicleRentalApply = vehicleRentalApplyMapStruct.toSource(vehicleRentalApplyDTO);
        vehicleRentalApply.setApplyId(vehicleApply.getApplyId());
        vehicleRentalApply.setCreateTime(new Date());
        vehicleRentalApply.setModifyTime(new Date());
        vehicleRentalApply.setMaterialPreviewType(EnumRentalMaterialPreviewType.ToBePreview.getValue());

        vehicleRentalApplyService.saveOrUpdate(vehicleRentalApply);

        // ### 5.归档车辆申请材料信息
        vehicleApplyMaterialService.saveVehicleApplyMaterials(vehicleApplyMaterialExDTOs, vehicleApply.getApplyId());

        // ### 6.归档申请人邮寄信息
        if (Objects.equals(vehicleApplyCommonInfoDTO.getIsMail(), EnumYesOrNot.Yes.getValue())) {
            // 归档申请人邮寄信息
            SPBusinessApplyRecipientDTO recipientDTO = vehicleApplyCommonInfoDTO.getRecipient();
            SPBusinessApplyRecipient spBusinessApplyRecipient = spBusinessApplyRecipientMapStruct.toSource(recipientDTO);
            spBusinessApplyRecipient.setApplyId(vehicleApply.getApplyId());
            spBusinessApplyRecipientService.saveOrUpdate(spBusinessApplyRecipient);
        }

        // ### 7.归档车辆照片信息
        vehicleApplyPhotoService.saveVehicleApplyPhoto(photos, vehicleApply.getApplyId());

        // ### 8.归档车辆申请经营范围
        vehicleApplyBusiScopeService.saveVehicleApplyBusiScope(busiScopes, vehicleApply.getApplyId());

        //10.发布事件
        applicationContext.publishEvent(new VehicleApplyEvent(vehicleApply.getApplyId(), EnumVehicleApplyEvent.Created));
    }

    @Override
    public String downloadFiles(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, HttpServletResponse response) {
        String bucketName = amazonS3Properties.getDefaultBucket();
        String docPath;
        String orgId = vehicleApplyCommonInfoDTO.getOrgId();
        Assert.notNull(orgId, "申请Id为空!");
        SysOrganize organize = sysOrganizeService.getById(orgId);
        Assert.notNull(organize, "机构Id为[" + orgId + "]的机构不存在！");
        InputStream templateInputStream = null;
        try {
            // 查找Word模板文件
            templateInputStream = findFileInputStream(orgId, VEHICLE_RECORD_QUALIFIED_TPL_NAME);
            assert templateInputStream != null;

            XWPFTemplate template = XWPFTemplate.compile(templateInputStream).render(initDownloadData(vehicleApplyCommonInfoDTO));
            // 将Word文档写入 ByteArrayOutputStream
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            template.write(byteArrayOutputStream);
            template.close();
            InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

            String datePath = DateUtil.today().replace("-", "/");
            String docId = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
            String docFileName = "申请材料_" + docId + ".docx";

            // rental/tpl/docx/
            String docxS3Path = PREFIX_PATH + "docx/";
            docPath = docxS3Path + datePath + "/" + docFileName;

            // 将doc上传到S3
            ObjectMetadata metadataDocx = new ObjectMetadata();
            metadataDocx.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            metadataDocx.setContentLength(byteArrayOutputStream.size());
            amazonS3.putObject(bucketName, docPath, inputStream, metadataDocx);
            inputStream.close();
        } catch (Exception e) {
            log.error("下载申请材料出错");
            throw new RuntimeException(e);
        } finally {
            IoUtil.close(templateInputStream);
        }
        TmisPhotoPathWriter tmisPhotoPathWriter = ApplicationContextHolder.getBean(TmisPhotoPathWriter.class);
        return tmisPhotoPathWriter.write(docPath);
    }

    private Map<String, Object> initDownloadData(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO) {
        Map<String, Object> data = new HashMap<>();
        data.put("owner", vehicleApplyCommonInfoDTO.getVehicleApplyRental().getOwner());
        data.put("vehicleTypeName", vehicleApplyCommonInfoDTO.getBaseInfo().getSpecification() + vehicleApplyCommonInfoDTO.getBaseInfo().getStructure());
        data.put("model", vehicleApplyCommonInfoDTO.getExtInfo().getModel());
        data.put("vin", vehicleApplyCommonInfoDTO.getBaseInfo().getVin());
        data.put("vehicleColor", vehicleApplyCommonInfoDTO.getExtInfo().getVehicleColor());
        data.put("seats", vehicleApplyCommonInfoDTO.getBaseInfo().getSeats());
        data.put("operator", vehicleApplyCommonInfoDTO.getOperator());
        data.put("mobile", vehicleApplyCommonInfoDTO.getMobile());
        if (vehicleApplyCommonInfoDTO.getApplyType().equals(EnumVehicleApplyType.Registration.getValue())) {
            data.put("check1", "☑");
            data.put("check2", "□");
        } else if (vehicleApplyCommonInfoDTO.getApplyType().equals(EnumVehicleApplyType.CancelRegistration.getValue())){
            data.put("check1", "□");
            data.put("check2", "☑");
        }
        data.put("check3", "□");
        data.put("check4", "☑");
        data.put("applyReason", vehicleApplyCommonInfoDTO.getApplyReason());
        data.put("comments", vehicleApplyCommonInfoDTO.getComments());
        if (EmptyUtil.isNotEmpty(vehicleApplyCommonInfoDTO.getApplyDate())) {
            data.put("year1", DateUtil.format(vehicleApplyCommonInfoDTO.getApplyDate(), "yyyy"));
            data.put("month1", DateUtil.format(vehicleApplyCommonInfoDTO.getApplyDate(), "MM"));
            data.put("day1", DateUtil.format(vehicleApplyCommonInfoDTO.getApplyDate(), "dd"));
        }
        if (EmptyUtil.isNotEmpty(vehicleApplyCommonInfoDTO.getAcceptDate())) {
            data.put("year2", DateUtil.format(vehicleApplyCommonInfoDTO.getAcceptDate(), "yyyy"));
            data.put("month2", DateUtil.format(vehicleApplyCommonInfoDTO.getAcceptDate(), "MM"));
            data.put("day2", DateUtil.format(vehicleApplyCommonInfoDTO.getAcceptDate(), "dd"));
        }
        initOwnerInfo(vehicleApplyCommonInfoDTO, data);
        return data;
    }

    private void initOwnerInfo(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, Map<String, Object> map) {
        String ownerId = vehicleApplyCommonInfoDTO.getOwnerInfo().getCertInfo().getOwnerId();
        RoadOwner roadOwner = roadOwnerService.getById(ownerId);
        if (EmptyUtil.isNotEmpty(roadOwner)) {
            map.put("ownerName", roadOwner.getOwnerName());
            map.put("socialCreditCode", roadOwner.getSocialCreditCode());
            map.put("address", roadOwner.getAddress());
        }
        OwnerCarRental ownerCarRental = ownerCarRentalService.getById(ownerId);
        if (EmptyUtil.isNotEmpty(ownerCarRental)) {
            if (ownerCarRental.getBusinessModel().equals(EnumBusinessModel.Timeshare.getValue())
            || ownerCarRental.getBusinessModel().equals(EnumBusinessModel.TwoTypes.getValue())) {
                map.put("check5", "☑");
                map.put("check6", "□");
            } else {
                map.put("check5", "□");
                map.put("check6", "☑");
            }
        } else {
            map.put("check5", "□");
            map.put("check6", "□");
        }
        LambdaQueryWrapper<RoadOwnerPers> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RoadOwnerPers::getOwnerId, ownerId);
        List<RoadOwnerPers> roadOwnerPersList = roadOwnerPersService.list(lambdaQueryWrapper);
        if (EmptyUtil.isNotEmpty(roadOwnerPersList)) {
            roadOwnerPersList.forEach(x -> {

                //负责人信息
                if (Objects.equals(x.getPersType(), EnumOwnerPersType.Principal.getValue())) {
                    map.put("priName", x.getPersName());
                    map.put("priMobile", x.getMobile());
                    map.put("priIdCardNum", x.getIdCardNum());
                }
                //法人信息
                if (Objects.equals(x.getPersType(), EnumOwnerPersType.Legal.getValue())) {
                    map.put("legalName", x.getPersName());
                    map.put("legalMobile", x.getMobile());
                    map.put("legalIdCardNum", x.getIdCardNum());
                }
            });
        }
        map.put("year3", DateUtil.year(new Date()));
        map.put("month3", DateUtil.month(new Date()) + 1);
        map.put("day3", DateUtil.dayOfMonth(new Date()));
    }

    /**
     * 获取打印模板流
     *
     * @param orgId     管辖机构Id
     * @param tplName   模板名
     * @return {@link InputStream }
     **/
    private InputStream findFileInputStream(String orgId, String tplName) {
        String bucketName = amazonS3Properties.getDefaultBucket();
        String prefixPath = PREFIX_PATH + orgId + "/";
        String filePath = prefixPath + tplName;
        // 地市自定义
        if (amazonS3.doesObjectExist(bucketName, filePath)) {
            S3Object s3Object = amazonS3.getObject(bucketName, filePath);
            log.info("调用模板:{}", filePath);
            return s3Object.getObjectContent();
        } else {
            if (FINAL_ORGID.equals(orgId)) {
                return amazonS3.getObject(bucketName, PREFIX_PATH + FINAL_ORGID + "/" + tplName).getObjectContent();
            }
            return findFileInputStream(verifyOrg(orgId), tplName);
        }
    }

    /**
     * 验证机构并返回父级机构Id
     *
     * @param orgId 管辖机构Id
     * @return {@link String }
     **/
    private String verifyOrg(String orgId) {
        SysOrganize sysOrganize = sysOrganizeService.getById(orgId);
        Assert.notNull(sysOrganize, "机构Id为[" + orgId + "]的管辖机构不存在!");
        String parentId = sysOrganize.getParentId();
        Assert.notNull(parentId, "管辖机构Id为[" + orgId + "]的父级机构Id不存在!");
        SysOrganize parentOrganize = sysOrganizeService.getById(parentId);
        Assert.notNull(parentOrganize, "机构Id为[" + parentId + "]的管辖机构不存在!");
        return parentId;
    }

    /**
     * 验证
     *
     * @param vehicleApplyCommonInfoDTO 车辆租赁申请常见信息dto
     * @return boolean
     */
    public boolean validate(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO) {

        VehicleRentalApplyDTO vehicleRentalApplyDTO = vehicleApplyCommonInfoDTO.getVehicleApplyRental();
        VehicleApplyBaseDTO vehicleApplyBaseDTO = vehicleApplyCommonInfoDTO.getBaseInfo();
        VehicleApplyExtDTO vehicleApplyExtDTO = vehicleApplyCommonInfoDTO.getExtInfo();

        return EmptyUtil.isNotEmpty(vehicleApplyCommonInfoDTO.getVehicleNum())
                && EmptyUtil.isNotEmpty(vehicleApplyCommonInfoDTO.getPlateColor())
                && EmptyUtil.isNotEmpty(vehicleApplyExtDTO.getModel())
                && EmptyUtil.isNotEmpty(vehicleApplyExtDTO.getEngineNum())
                && EmptyUtil.isNotEmpty(vehicleRentalApplyDTO.getOwner())
                && EmptyUtil.isNotEmpty(vehicleApplyExtDTO.getVehicleUse())
                && EmptyUtil.isNotEmpty(vehicleApplyExtDTO.getDrivingRegDate())
                && EmptyUtil.isNotEmpty(vehicleApplyExtDTO.getVehiLength())
                && EmptyUtil.isNotEmpty(vehicleApplyExtDTO.getVehiWidth())
                && EmptyUtil.isNotEmpty(vehicleApplyExtDTO.getVehiHigh())
                && EmptyUtil.isNotEmpty(vehicleApplyExtDTO.getIsEntry())
                && EmptyUtil.isNotEmpty(vehicleApplyExtDTO.getTermStandType())
                && EmptyUtil.isNotEmpty(vehicleApplyBaseDTO.getFuelType())
                && EmptyUtil.isNotEmpty(vehicleApplyBaseDTO.getSeats())
                && EmptyUtil.isNotEmpty(vehicleApplyBaseDTO.getLicenseFirstDate())
                && EmptyUtil.isNotEmpty(vehicleRentalApplyDTO.getOperateState())
                && EmptyUtil.isNotEmpty(vehicleRentalApplyDTO.getBusinessModel());
    }
}
