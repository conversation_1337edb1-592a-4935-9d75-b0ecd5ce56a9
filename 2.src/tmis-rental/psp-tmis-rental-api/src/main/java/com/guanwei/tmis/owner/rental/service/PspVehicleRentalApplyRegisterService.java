package com.guanwei.tmis.owner.rental.service;

import com.guanwei.tmis.common.enums.EnumApplySource;
import com.guanwei.tmis.common.enums.EnumApplyState;
import com.guanwei.tmis.common.enums.EnumRentalMaterialPreviewType;
import com.guanwei.tmis.common.enums.EnumRentalRecordType;
import com.guanwei.tmis.common.psp.token.AccountInfo;
import com.guanwei.tmis.owner.common.rental.dto.VehicleApplyCommonInfoDTO;

/**
 * psp车辆租赁备案变更申请服务
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
public interface PspVehicleRentalApplyRegisterService {

    /**
     * 微信端-暂存
     *
     * @param vehicleApplyCommonInfoDTO 车辆出租申请通用信息dto
     * @param accountInfo               帐户信息上下文
     */
    void wxTemporary(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfo);

    /**
     * 企业端-提交
     *
     * @param vehicleApplyCommonInfoDTO 车辆出租申请通用信息dto
     * @param accountInfo               帐户信息上下文
     */
    void submit(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfo);

    /**
     * 微信端-提交
     *
     * @param vehicleApplyCommonInfoDTO 车辆出租申请通用信息dto
     * @param accountInfo               帐户信息上下文
     */
    void wxSubmit(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfo);

    /**
     * 备案-材料预审-暂存
     *
     * @param vehicleApplyCommonInfoDTO 车辆申请通用信息dto
     * @param accountInfoContext        帐户信息上下文
     */
    void previewTemporary(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfoContext);

    /**
     * 企业端-材料预审-提交
     *
     * @param vehicleApplyCommonInfoDTO 车辆申请通用信息dto
     * @param accountInfoContext        帐户信息上下文
     */
    void previewSubmit(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfoContext);

    /**
     * 微信端-材料预审-提交
     *
     * @param vehicleApplyCommonInfoDTO 车辆申请通用信息dto
     * @param accountInfoContext        帐户信息上下文
     */
    void previewWxSubmit(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO, AccountInfo accountInfoContext);

    /**
     * 通用保存
     *
     * @param vehicleApplyCommonInfoDTO     车辆应用普通信息dto
     * @param accountInfo                   帐户信息
     * @param enumApplyState                枚举申请状态
     * @param enumApplySource               枚举申请来源
     * @param enumRentalMaterialPreviewType 枚举租赁材质预览类型
     * @param enumRentalRecordType          租赁备案类型
     * @return {@link String }
     */
    String commonSave(VehicleApplyCommonInfoDTO vehicleApplyCommonInfoDTO,
                      AccountInfo accountInfo,
                      EnumApplyState enumApplyState,
                      EnumApplySource enumApplySource,
                      EnumRentalMaterialPreviewType enumRentalMaterialPreviewType,
                      EnumRentalRecordType enumRentalRecordType);
}
