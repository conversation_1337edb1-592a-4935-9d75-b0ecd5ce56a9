package com.guanwei.tmis.owner.rental.controller;

import com.guanwei.core.utils.result.R;
import com.guanwei.tmis.owner.common.rental.dto.VehicleRentalDetailDTO;
import com.guanwei.tmis.owner.common.rental.service.VehicleRentalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 租赁车辆查询
 *
 * <AUTHOR>
 * @date 2023/12/15
 */
@Slf4j
@RestController
@RequestMapping("/v1/vehicle/rental")
@RequiredArgsConstructor
public class PspVehicleRentalCommonController {

    private final VehicleRentalService vehicleRentalService;


    /**
     * 通过ID获取租赁车辆信息
     *
     * @param vehicleId 车辆id
     * @return {@link R}<{@link VehicleRentalDetailDTO}>
     */
    @GetMapping("/{vehicleId}")
    public R<VehicleRentalDetailDTO> getVehicleInfo(@PathVariable String vehicleId) {
        return R.OK(vehicleRentalService.getVehicleInfo(vehicleId));
    }
}
