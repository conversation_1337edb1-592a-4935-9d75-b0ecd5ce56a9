package com.guanwei.tmis.owner.rental.service;

import com.guanwei.tmis.common.psp.token.AccountInfo;
import com.guanwei.tmis.owner.common.rental.dto.CarRentalApplyCommonInfoDTO;
import com.guanwei.tmis.owner.common.rental.dto.OwnerCarRentalListDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/3
 */
public interface PspOwnerCarRentalListService {

    /**
     * 根据业户名称获取租赁业户基本信息
     *
     * @param areaCode  地区编码
     * @param ownerName 业户名称
     * @return {@link OwnerCarRentalListDTO }
     **/
    List<OwnerCarRentalListDTO> getOwnerCarRentalList(String areaCode, String ownerName);

    /**
     * 下载备案申请表(预审的表)
     *
     * @param carRentalApplyCommonInfoDTO 租赁业户通用信息
     */
    String downloadApplyFileVx(CarRentalApplyCommonInfoDTO carRentalApplyCommonInfoDTO);

}
