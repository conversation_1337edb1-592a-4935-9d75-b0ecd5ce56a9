package com.guanwei.tmis.owner.rental.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.guanwei.tmis.common.enums.EnumApplyState;
import com.guanwei.tmis.common.enums.EnumCheckState;
import com.guanwei.tmis.common.enums.EnumOwnerApplyEvent;
import com.guanwei.tmis.common.enums.EnumVehicleApplyEvent;
import com.guanwei.tmis.common.psp.token.AccountInfo;
import com.guanwei.tmis.owner.base.entity.RoadOwnerApply;
import com.guanwei.tmis.owner.base.event.OwnerApplyEvent;
import com.guanwei.tmis.owner.base.service.RoadOwnerApplyService;
import com.guanwei.tmis.owner.rental.service.PspOwnerCarRentalApplyWorkflowService;
import com.guanwei.tmis.vehicle.common.entity.VehicleApply;
import com.guanwei.tmis.vehicle.common.entity.VehicleBatchApply;
import com.guanwei.tmis.vehicle.common.event.VehicleApplyEvent;
import com.guanwei.tmis.vehicle.common.mapper.VehicleBatchApplyMapper;
import com.guanwei.tmis.vehicle.common.service.VehicleApplyService;
import com.guanwei.workflow.dto.WorkflowProcessDataDTO;
import com.guanwei.workflow.service.WorkflowService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 企业端工作流服务
 *
 * <AUTHOR>
 * @date 2023/9/14
 */
@Service
@RequiredArgsConstructor
public class PspOwnerCarRentalApplyWorkflowServiceImpl implements PspOwnerCarRentalApplyWorkflowService {

    private final WorkflowService workflowService;

    private final RoadOwnerApplyService roadOwnerApplyService;

    private final ApplicationContext applicationContext;

    private final VehicleBatchApplyMapper vehicleBatchApplyMapper;

    private final VehicleApplyService vehicleApplyService;



    /**
     * 作废
     *
     * @param workflowProcessDataDTO 工作流过程数据dto
     * @param accountInfo            帐户信息
     * <AUTHOR>
     * @date 2023-09-14
     **/
    @Override
    public void invalid(WorkflowProcessDataDTO workflowProcessDataDTO, AccountInfo accountInfo) {
        RoadOwnerApply roadOwnerApply = roadOwnerApplyService.
                getById(workflowProcessDataDTO.getBizObjectId());
        Assert.notNull(roadOwnerApply, "Id为【" + workflowProcessDataDTO.getBizObjectId() + "】的业户申请信息已不存在！");
        Assert.isTrue(roadOwnerApply.getState()!= EnumApplyState.Invalid.getValue(),"该申请已作废，请勿重复操作");
        LambdaUpdateWrapper<RoadOwnerApply> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(RoadOwnerApply::getState, EnumApplyState.Invalid.getValue());
        lambdaUpdateWrapper.set(RoadOwnerApply::getCheckState, EnumCheckState.Invalid.getValue());
        lambdaUpdateWrapper.set(RoadOwnerApply::getModifier, accountInfo.getAccountName());
        lambdaUpdateWrapper.set(RoadOwnerApply::getModifyTime, new Date());
        lambdaUpdateWrapper.set(RoadOwnerApply::getComments, workflowProcessDataDTO.getComments());
        lambdaUpdateWrapper.eq(RoadOwnerApply::getApplyId, workflowProcessDataDTO.getBizObjectId());
        roadOwnerApplyService.update(lambdaUpdateWrapper);

        //10.发布事件
        applicationContext.publishEvent(new OwnerApplyEvent(roadOwnerApply.getApplyId(), EnumOwnerApplyEvent.Invalid));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void associationBatchInvalid(WorkflowProcessDataDTO workflowProcessDataDTO, AccountInfo accountInfo) {
        RoadOwnerApply roadOwnerApply = roadOwnerApplyService.getById(workflowProcessDataDTO.getBizObjectId());
        Assert.notNull(roadOwnerApply, "Id为【" + workflowProcessDataDTO.getBizObjectId() + "】的业户申请信息已不存在！");
        Assert.isTrue(!Objects.equals(EnumApplyState.Invalid.getValue(), roadOwnerApply.getState()),"该申请已作废，请勿重复操作");
        LambdaUpdateWrapper<RoadOwnerApply> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(RoadOwnerApply::getState, EnumApplyState.Invalid.getValue());
        lambdaUpdateWrapper.set(RoadOwnerApply::getCheckState, EnumCheckState.Invalid.getValue());
        lambdaUpdateWrapper.set(RoadOwnerApply::getModifier, accountInfo.getAccountName());
        lambdaUpdateWrapper.set(RoadOwnerApply::getModifyTime, new Date());
        lambdaUpdateWrapper.set(RoadOwnerApply::getComments, workflowProcessDataDTO.getComments());
        lambdaUpdateWrapper.eq(RoadOwnerApply::getApplyId, workflowProcessDataDTO.getBizObjectId());
        roadOwnerApplyService.update(lambdaUpdateWrapper);

        LambdaQueryWrapper<VehicleBatchApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VehicleBatchApply::getOwnerApplyId, workflowProcessDataDTO.getBizObjectId());
        List<VehicleBatchApply> list = vehicleBatchApplyMapper.selectList(wrapper);
        for (VehicleBatchApply vehicleBatchApply : list) {
            String vehicleApplyId = vehicleBatchApply.getVehicleApplyId();
            VehicleApply vehicleApply = vehicleApplyService.getById(vehicleApplyId);
            Assert.notNull(vehicleApply, "查询不到业户申请对应的车辆申请信息[" + vehicleApplyId + "]");
            LambdaUpdateWrapper<VehicleApply> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(VehicleApply::getApplyId, vehicleApplyId);
            updateWrapper.set(VehicleApply::getState, EnumApplyState.Invalid.getValue());
            updateWrapper.set(VehicleApply::getCheckState, EnumCheckState.Invalid.getValue());
            updateWrapper.set(VehicleApply::getModifier, accountInfo.getAccountName());
            updateWrapper.set(VehicleApply::getModifyTime, new Date());
            updateWrapper.set(VehicleApply::getComments, workflowProcessDataDTO.getComments());
            vehicleApplyService.update(updateWrapper);

            //10.发布事件
            applicationContext.publishEvent(new VehicleApplyEvent(vehicleApply.getApplyId(), EnumVehicleApplyEvent.Invalid));
        }

        //10.发布事件
        applicationContext.publishEvent(new OwnerApplyEvent(roadOwnerApply.getApplyId(), EnumOwnerApplyEvent.Invalid));
    }

}
