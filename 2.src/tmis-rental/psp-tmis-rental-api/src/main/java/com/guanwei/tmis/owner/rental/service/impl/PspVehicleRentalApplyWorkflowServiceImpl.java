package com.guanwei.tmis.owner.rental.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.guanwei.core.utils.EmptyUtil;
import com.guanwei.core.utils.result.R;
import com.guanwei.tmis.common.dto.BusinessItemMaterialDTO;
import com.guanwei.tmis.common.enums.*;
import com.guanwei.tmis.common.feign.client.BusinessItemMaterialFeignClient;
import com.guanwei.tmis.common.psp.token.AccountInfo;
import com.guanwei.tmis.owner.common.rental.entity.VehicleRentalApply;
import com.guanwei.tmis.owner.common.rental.service.VehicleRentalApplyService;
import com.guanwei.tmis.owner.rental.service.PspVehicleRentalApplyWorkflowService;
import com.guanwei.tmis.vehicle.common.dto.VehicleApplyMaterialExDTO;
import com.guanwei.tmis.vehicle.common.entity.VehicleApply;
import com.guanwei.tmis.vehicle.common.event.VehicleApplyEvent;
import com.guanwei.tmis.vehicle.common.service.VehicleApplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * psp车辆租赁申请工作流服务实现
 *
 * <AUTHOR>
 * @date 2023/10/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PspVehicleRentalApplyWorkflowServiceImpl implements PspVehicleRentalApplyWorkflowService {

    private final VehicleApplyService vehicleApplyService;

    private final VehicleRentalApplyService vehicleRentalApplyService;

    private final ApplicationContext applicationContext;

    /**
     * 作废
     *
     * @param applyId  申请id
     * @param accountInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void invalid(String applyId, AccountInfo accountInfo) {
        VehicleApply vehicleApply = vehicleApplyService.getById(applyId);
        Assert.notNull(vehicleApply, "Id为【" + applyId + "】的租赁车辆申请信息已不存在！");
        Assert.isTrue(!Objects.equals(vehicleApply.getState(), EnumApplyState.Invalid.getValue()), "该申请已作废，请勿重复操作");
        LambdaUpdateWrapper<VehicleApply> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(VehicleApply::getState, EnumApplyState.Invalid.getValue());
        lambdaUpdateWrapper.set(VehicleApply::getCheckState, EnumCheckState.Invalid.getValue());
        lambdaUpdateWrapper.set(VehicleApply::getModifier, accountInfo.getAccountName());
        lambdaUpdateWrapper.set(VehicleApply::getModifyTime, new Date());
        lambdaUpdateWrapper.eq(VehicleApply::getApplyId, applyId);
        vehicleApplyService.update(lambdaUpdateWrapper);

        VehicleRentalApply vehicleRentalApply = vehicleRentalApplyService.getById(applyId);
        Integer materialPreviewType = vehicleRentalApply.getMaterialPreviewType();
        if (!materialPreviewType.equals(EnumRentalMaterialPreviewType.None.getValue())) {
            vehicleRentalApply.setMaterialPreviewType(EnumRentalMaterialPreviewType.None.getValue());
            vehicleRentalApplyService.update(vehicleRentalApply);
        }
        //10.发布事件
        applicationContext.publishEvent(new VehicleApplyEvent(vehicleApply.getApplyId(), EnumVehicleApplyEvent.Invalid));
    }

    @Override
    public List<VehicleApplyMaterialExDTO> initMaterials(String orgId, Integer applyType) {
        List<VehicleApplyMaterialExDTO> vehicleApplyMaterialExs = new ArrayList<>();
        //业务事项材料清单
        BusinessItemMaterialFeignClient businessItemMaterialFeignClient = applicationContext.getBean(BusinessItemMaterialFeignClient.class);
        R<List<BusinessItemMaterialDTO>> businessItemMaterial;
        if (EmptyUtil.isEmpty(orgId)) {
            return vehicleApplyMaterialExs;
        } else {
            businessItemMaterial = businessItemMaterialFeignClient.getBusinessItemMaterialByOrgId(orgId,
                    EnumIndustryClass.Vehicle.getValue(), String.valueOf(EnumVehicleTransType.CarRental.getValue()),
                    applyType);
        }
        if (businessItemMaterial.isSuccess()) {
            List<BusinessItemMaterialDTO> list = businessItemMaterial.getData();
            for (int i = 0; i < list.size(); i++) {
                BusinessItemMaterialDTO businessItemMaterialDTO = businessItemMaterial.getData().get(i);
                VehicleApplyMaterialExDTO applyMaterialExDTO = new VehicleApplyMaterialExDTO();
                applyMaterialExDTO.setMaterialName(businessItemMaterialDTO.getMaterialName());
                applyMaterialExDTO.setMaterialCode(businessItemMaterialDTO.getMaterialCode());
                applyMaterialExDTO.setMaterialType(businessItemMaterialDTO.getMaterialType());
                applyMaterialExDTO.setOrderNo(i + 1);
                applyMaterialExDTO.setCreateTime(new Date());
                vehicleApplyMaterialExs.add(applyMaterialExDTO);
            }
        }
        return vehicleApplyMaterialExs;
    }
}
