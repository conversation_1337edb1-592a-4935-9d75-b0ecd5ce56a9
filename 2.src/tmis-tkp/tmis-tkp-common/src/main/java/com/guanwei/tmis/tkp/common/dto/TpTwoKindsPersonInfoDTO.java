package com.guanwei.tmis.tkp.common.dto;

import com.guanwei.json.EnumDescribe;
import com.guanwei.tmis.common.enums.EnumEducation;
import com.guanwei.tmis.common.enums.EnumIdCertificateType;
import com.guanwei.tmis.common.enums.EnumNationality;
import com.guanwei.tmis.common.enums.EnumSex;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 两类人员档案扩展信息 实体DTO传输对象
 *
 * <AUTHOR>
 * @date 2025-03-28 15:17:05
 */
@Data
public class TpTwoKindsPersonInfoDTO {

    /**
     * 两类人员档案信息
     */
    private TpTwoKindsPersonDTO personInfo;

    /**
     * 两类人员证件信息DTO
     */
    private List<TpTwoKindsPersonCertDTO> personCertInfoS;

}
